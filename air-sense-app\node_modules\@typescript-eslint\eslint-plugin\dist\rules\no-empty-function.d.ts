import type { TSESTree } from '@typescript-eslint/utils';
import type { InferMessageIdsTypeFromRule, InferOptionsTypeFromRule } from '../util';
declare const baseRule: import("@typescript-eslint/utils/ts-eslint").RuleModule<"unexpected", [{
    allow?: string[];
}], unknown, {
    FunctionDeclaration(node: TSESTree.FunctionDeclaration): void;
    FunctionExpression(node: TSESTree.FunctionExpression): void;
}>;
export type Options = InferOptionsTypeFromRule<typeof baseRule>;
export type MessageIds = InferMessageIdsTypeFromRule<typeof baseRule>;
declare const _default: import("@typescript-eslint/utils/ts-eslint").RuleModule<"unexpected", [{
    allow?: string[];
}], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
export default _default;
//# sourceMappingURL=no-empty-function.d.ts.map