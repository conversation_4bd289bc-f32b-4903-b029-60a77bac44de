'use client';

import React, { useState, useEffect } from 'react';
import Layout from '@/components/Layout';

const locations = [
  { value: 'lectureHall', label: 'Lecture Hall' },
  { value: 'Office', label: "Dean's Office" },
  { value: 'Basement', label: 'Basement' },
  { value: 'electronicLab', label: 'Lab' }
];

const metrics = [
  'Temperature',
  'Humidity', 
  'PM2.5',
  'PM10',
  'CO2 Level',
  'Gas Resistance',
  'Pressure',
  'Nitrogen Dioxide (NO2)',
  'Ozone (O3)'
];

// Mock data generator
const generateMockData = (location: string, metric: string) => {
  const baseValues: { [key: string]: number } = {
    'Temperature': 22 + Math.random() * 6,
    'Humidity': 40 + Math.random() * 30,
    'PM2.5': Math.random() * 150,
    'PM10': Math.random() * 300,
    'CO2 Level': 400 + Math.random() * 800,
    'Gas Resistance': 10 + Math.random() * 90,
    'Pressure': 960 + Math.random() * 60,
    'Nitrogen Dioxide (NO2)': Math.random() * 150,
    'Ozone (O3)': Math.random() * 140
  };
  
  return Math.round((baseValues[metric] || 0) * 10) / 10;
};

const getQualityStatus = (metric: string, value: number) => {
  // Simplified quality assessment
  const thresholds: { [key: string]: { good: number; moderate: number } } = {
    'Temperature': { good: 25, moderate: 28 },
    'Humidity': { good: 60, moderate: 70 },
    'PM2.5': { good: 100, moderate: 125 },
    'PM10': { good: 200, moderate: 250 },
    'CO2 Level': { good: 800, moderate: 1200 },
    'Gas Resistance': { good: 50, moderate: 10 },
    'Pressure': { good: 1020, moderate: 980 },
    'Nitrogen Dioxide (NO2)': { good: 110, moderate: 130 },
    'Ozone (O3)': { good: 100, moderate: 120 }
  };

  const threshold = thresholds[metric];
  if (!threshold) return 'moderate';

  if (metric === 'Gas Resistance') {
    if (value >= threshold.good) return 'good';
    if (value >= threshold.moderate) return 'moderate';
    return 'poor';
  } else {
    if (value <= threshold.good) return 'good';
    if (value <= threshold.moderate) return 'moderate';
    return 'poor';
  }
};

const getStatusColor = (status: string) => {
  switch (status) {
    case 'good': return 'text-green-600';
    case 'moderate': return 'text-yellow-600';
    case 'poor': return 'text-red-600';
    default: return 'text-gray-600';
  }
};

export default function Comparison() {
  const [location1, setLocation1] = useState<string>('');
  const [location2, setLocation2] = useState<string>('');
  const [date1, setDate1] = useState<string>('');
  const [date2, setDate2] = useState<string>('');
  const [metric1, setMetric1] = useState<string>('Temperature');
  const [metric2, setMetric2] = useState<string>('Temperature');
  const [data1, setData1] = useState<number>(0);
  const [data2, setData2] = useState<number>(0);
  const [validationMessage, setValidationMessage] = useState<string>('');

  useEffect(() => {
    const today = new Date().toISOString().split('T')[0];
    setDate1(today);
    setDate2(today);
  }, []);

  useEffect(() => {
    if (location1 && metric1) {
      setData1(generateMockData(location1, metric1));
    }
  }, [location1, metric1]);

  useEffect(() => {
    if (location2 && metric2) {
      setData2(generateMockData(location2, metric2));
    }
  }, [location2, metric2]);

  useEffect(() => {
    if (location1 && location2 && location1 === location2) {
      setValidationMessage('Please select different locations for comparison');
    } else {
      setValidationMessage('');
    }
  }, [location1, location2]);

  const status1 = getQualityStatus(metric1, data1);
  const status2 = getQualityStatus(metric2, data2);

  const getComparisonText = () => {
    if (!location1 || !location2) {
      return 'Select locations and metrics to view comparison';
    }

    const loc1Name = locations.find(l => l.value === location1)?.label || location1;
    const loc2Name = locations.find(l => l.value === location2)?.label || location2;

    return `Comparing ${metric1} at ${loc1Name} (${data1}) with ${metric2} at ${loc2Name} (${data2}). 
            ${loc1Name} shows ${status1} air quality while ${loc2Name} shows ${status2} air quality.`;
  };

  return (
    <Layout>
      <div className="min-h-screen bg-gray-50">
        <header className="bg-white shadow-sm py-8">
          <div className="max-w-6xl mx-auto px-8">
            <h1 className="text-3xl font-bold text-gray-800 mb-2">
              Welcome to the Smart Indoor Air Quality Monitoring System
            </h1>
            <p className="text-gray-600">
              Track, analyze, and improve indoor air quality with ease
            </p>
          </div>
        </header>

        <main className="max-w-6xl mx-auto px-8 py-8">
          <h2 className="text-2xl font-bold text-gray-800 mb-6">Select your Locations</h2>
          
          {validationMessage && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
              {validationMessage}
            </div>
          )}

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            {/* Location 1 */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-xl font-semibold mb-4">Location 1</h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Location:</label>
                  <select 
                    value={location1}
                    onChange={(e) => setLocation1(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">Select Location</option>
                    {locations.map(loc => (
                      <option key={loc.value} value={loc.value}>{loc.label}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Date:</label>
                  <input 
                    type="date" 
                    value={date1}
                    onChange={(e) => setDate1(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Metrics:</label>
                  <select 
                    value={metric1}
                    onChange={(e) => setMetric1(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    {metrics.map(metric => (
                      <option key={metric} value={metric}>{metric}</option>
                    ))}
                  </select>
                </div>

                <div className="text-center py-8">
                  <div className="text-4xl font-bold text-gray-800 mb-2">{data1}</div>
                  <div className={`text-lg font-medium ${getStatusColor(status1)}`}>
                    {status1.charAt(0).toUpperCase() + status1.slice(1)}
                  </div>
                </div>

                <div className="text-center text-gray-600">
                  {location1 ? `Data from ${locations.find(l => l.value === location1)?.label}` : 'Select location to view data'}
                </div>
              </div>
            </div>

            {/* Location 2 */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-xl font-semibold mb-4">Location 2</h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Location:</label>
                  <select 
                    value={location2}
                    onChange={(e) => setLocation2(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">Select Location</option>
                    {locations.map(loc => (
                      <option key={loc.value} value={loc.value}>{loc.label}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Date:</label>
                  <input 
                    type="date" 
                    value={date2}
                    onChange={(e) => setDate2(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Metrics:</label>
                  <select 
                    value={metric2}
                    onChange={(e) => setMetric2(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    {metrics.map(metric => (
                      <option key={metric} value={metric}>{metric}</option>
                    ))}
                  </select>
                </div>

                <div className="text-center py-8">
                  <div className="text-4xl font-bold text-gray-800 mb-2">{data2}</div>
                  <div className={`text-lg font-medium ${getStatusColor(status2)}`}>
                    {status2.charAt(0).toUpperCase() + status2.slice(1)}
                  </div>
                </div>

                <div className="text-center text-gray-600">
                  {location2 ? `Data from ${locations.find(l => l.value === location2)?.label}` : 'Select location to view data'}
                </div>
              </div>
            </div>
          </div>

          {/* Legend */}
          <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
            <div className="flex justify-center gap-8">
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 bg-green-500 rounded-full"></div>
                <span className="text-sm text-gray-600">Good</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 bg-yellow-500 rounded-full"></div>
                <span className="text-sm text-gray-600">Moderate</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 bg-red-500 rounded-full"></div>
                <span className="text-sm text-gray-600">Poor</span>
              </div>
            </div>
          </div>

          {/* Comparison Summary */}
          <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
            <h3 className="text-xl font-semibold mb-4">Comparison Summary</h3>
            <p className="text-gray-700 leading-relaxed">
              {getComparisonText()}
            </p>
          </div>

          {/* Daily Distribution Chart Placeholder */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-xl font-semibold mb-4">Daily Distribution</h3>
            <div className="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
              <p className="text-gray-500">Chart visualization would be implemented here with Chart.js</p>
            </div>
          </div>
        </main>
      </div>
    </Layout>
  );
}
