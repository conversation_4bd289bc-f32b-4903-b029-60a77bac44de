{"version": 3, "file": "parameter-properties.js", "sourceRoot": "", "sources": ["../../src/rules/parameter-properties.ts"], "names": [], "mappings": ";;AACA,oDAA0D;AAC1D,wEAAsE;AAEtE,kCAAqC;AAsBrC,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,sBAAsB;IAC5B,IAAI,EAAE;QACJ,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EACT,gEAAgE;SACnE;QACD,QAAQ,EAAE;YACR,mBAAmB,EACjB,gEAAgE;YAClE,uBAAuB,EACrB,oEAAoE;SACvE;QACD,MAAM,EAAE;YACN;gBACE,KAAK,EAAE;oBACL,QAAQ,EAAE;wBACR,IAAI,EAAE,QAAQ;wBACd,IAAI,EAAE;4BACJ,UAAU;4BACV,SAAS;4BACT,WAAW;4BACX,QAAQ;4BACR,kBAAkB;4BAClB,oBAAoB;4BACpB,iBAAiB;yBAClB;qBACF;iBACF;gBACD,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,KAAK,EAAE;wBACL,IAAI,EAAE,OAAO;wBACb,KAAK,EAAE;4BACL,IAAI,EAAE,0BAA0B;yBACjC;qBACF;oBACD,MAAM,EAAE;wBACN,IAAI,EAAE,QAAQ;wBACd,IAAI,EAAE,CAAC,gBAAgB,EAAE,oBAAoB,CAAC;qBAC/C;iBACF;gBACD,oBAAoB,EAAE,KAAK;aAC5B;SACF;KACF;IACD,cAAc,EAAE;QACd;YACE,KAAK,EAAE,EAAE;YACT,MAAM,EAAE,gBAAgB;SACzB;KACF;IACD,MAAM,CAAC,OAAO,EAAE,CAAC,EAAE,KAAK,GAAG,EAAE,EAAE,MAAM,GAAG,gBAAgB,EAAE,CAAC;QACzD;;;WAGG;QACH,SAAS,YAAY,CACnB,IAAgE;YAEhE,MAAM,SAAS,GAAe,EAAE,CAAC;YAEjC,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;gBACvB,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACrC,CAAC;YACD,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAClB,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC7B,CAAC;YAED,OAAO,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,CAAa,CAAC;QACzD,CAAC;QAED,IAAI,MAAM,KAAK,gBAAgB,EAAE,CAAC;YAChC,OAAO;gBACL,mBAAmB,CAAC,IAAI;oBACtB,MAAM,SAAS,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;oBAErC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;wBAC/B,0DAA0D;wBAC1D,IACE,IAAI,CAAC,SAAS,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;4BACjD,IAAI,CAAC,SAAS,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB,EACxD,CAAC;4BACD,OAAO;wBACT,CAAC;wBAED,MAAM,IAAI,GACR,IAAI,CAAC,SAAS,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;4BAC/C,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI;4BACrB,CAAC,CAAC,qDAAqD;gCACpD,IAAI,CAAC,SAAS,CAAC,IAA4B,CAAC,IAAI,CAAC;wBAExD,OAAO,CAAC,MAAM,CAAC;4BACb,IAAI;4BACJ,SAAS,EAAE,qBAAqB;4BAChC,IAAI,EAAE;gCACJ,SAAS,EAAE,IAAI;6BAChB;yBACF,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC;aACF,CAAC;QACJ,CAAC;QAQD,MAAM,wBAAwB,GAAiC,EAAE,CAAC;QAElE,SAAS,cAAc,CAAC,IAAY;YAClC,MAAM,mBAAmB,GACvB,wBAAwB,CAAC,wBAAwB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAChE,MAAM,QAAQ,GAAG,mBAAmB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAC/C,IAAI,QAAQ,EAAE,CAAC;gBACb,OAAO,QAAQ,CAAC;YAClB,CAAC;YAED,MAAM,OAAO,GAAkB,EAAE,CAAC;YAClC,mBAAmB,CAAC,GAAG,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YACvC,OAAO,OAAO,CAAC;QACjB,CAAC;QAED,MAAM,UAAU,GAAG,IAAA,4BAAa,EAAC,OAAO,CAAC,CAAC;QAE1C,SAAS,oBAAoB,CAC3B,aAA0C,EAC1C,oBAAyC;YAEzC,IACE,CAAC,aAAa,CAAC,cAAc;gBAC7B,CAAC,oBAAoB,CAAC,cAAc,EACpC,CAAC;gBACD,OAAO,CACL,aAAa,CAAC,cAAc,KAAK,oBAAoB,CAAC,cAAc,CACrE,CAAC;YACJ,CAAC;YAED,OAAO,CACL,UAAU,CAAC,OAAO,CAAC,aAAa,CAAC,cAAc,CAAC;gBAChD,UAAU,CAAC,OAAO,CAAC,oBAAoB,CAAC,cAAc,CAAC,CACxD,CAAC;QACJ,CAAC;QAED,OAAO;YACL,mCAAmC;gBACjC,wBAAwB,CAAC,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC;YAC3C,CAAC;YAED,kDAAkD;gBAChD,MAAM,mBAAmB,GAAG,wBAAwB,CAAC,GAAG,EAAG,CAAC;gBAE5D,KAAK,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,mBAAmB,EAAE,CAAC;oBAChD,IACE,KAAK,CAAC,aAAa;wBACnB,KAAK,CAAC,qBAAqB;wBAC3B,KAAK,CAAC,oBAAoB;wBAC1B,oBAAoB,CAClB,KAAK,CAAC,aAAa,EACnB,KAAK,CAAC,oBAAoB,CAC3B,EACD,CAAC;wBACD,OAAO,CAAC,MAAM,CAAC;4BACb,IAAI,EAAE;gCACJ,SAAS,EAAE,IAAI;6BAChB;4BACD,SAAS,EAAE,yBAAyB;4BACpC,IAAI,EAAE,KAAK,CAAC,aAAa;yBAC1B,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC;YACH,CAAC;YAED,SAAS,CAAC,IAAI;gBACZ,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;oBAChC,IACE,OAAO,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB;wBAClD,OAAO,CAAC,GAAG,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;wBAC9C,CAAC,OAAO,CAAC,KAAK;wBACd,CAAC,KAAK,CAAC,QAAQ,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,EACtC,CAAC;wBACD,cAAc,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,aAAa,GAAG,OAAO,CAAC;oBAC3D,CAAC;gBACH,CAAC;YACH,CAAC;YAED,sCAAsC,CACpC,IAA+B;gBAE/B,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;oBAC1C,IAAI,SAAS,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,EAAE,CAAC;wBACjD,cAAc,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,oBAAoB,GAAG,SAAS,CAAC;oBAClE,CAAC;gBACH,CAAC;gBAED,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,IAAI,EAAE,EAAE,CAAC;oBACpD,IACE,SAAS,CAAC,IAAI,KAAK,sBAAc,CAAC,mBAAmB;wBACrD,SAAS,CAAC,UAAU,CAAC,IAAI,KAAK,sBAAc,CAAC,oBAAoB;wBACjE,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI;4BAC5B,sBAAc,CAAC,gBAAgB;wBACjC,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI;4BACnC,sBAAc,CAAC,cAAc;wBAC/B,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI;4BACrC,sBAAc,CAAC,UAAU;wBAC3B,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,EAC7D,CAAC;wBACD,MAAM;oBACR,CAAC;oBAED,cAAc,CACZ,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAChC,CAAC,qBAAqB,GAAG,SAAS,CAAC,UAAU,CAAC;gBACjD,CAAC;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}