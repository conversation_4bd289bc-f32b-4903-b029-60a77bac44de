import type { Metadata } from "next";
import "./globals.css";

export const metadata: Metadata = {
  title: "AirSense - Smart Indoor Air Quality Monitoring",
  description: "Track, analyze, and improve indoor air quality with ease",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className="font-sans antialiased">
        {children}
      </body>
    </html>
  );
}
