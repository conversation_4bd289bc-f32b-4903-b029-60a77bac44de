import React from 'react';
import Image from 'next/image';

const Footer: React.FC = () => {
  return (
    <footer className="bg-gray-800 text-white py-8 px-8">
      <div className="max-w-6xl mx-auto flex flex-col md:flex-row justify-between items-center">
        <p className="text-center md:text-left mb-4 md:mb-0">
          &copy; 2025 Meridian Innovators. All Rights Reserved.
        </p>
        
        <div className="flex flex-wrap justify-center gap-6 mb-4 md:mb-0">
          <a href="#" className="text-gray-300 hover:text-white transition-colors">
            Privacy Policy
          </a>
          <a href="#" className="text-gray-300 hover:text-white transition-colors">
            Terms of Service
          </a>
          <a href="#" className="text-gray-300 hover:text-white transition-colors">
            Contact Us
          </a>
        </div>
        
        <div className="flex gap-4">
          <a href="#" className="hover:opacity-80 transition-opacity">
            <Image 
              src="/images/fb.avif" 
              alt="Facebook" 
              width={24} 
              height={24}
              className="rounded"
            />
          </a>
          <a href="#" className="hover:opacity-80 transition-opacity">
            <Image 
              src="/images/twitter.webp" 
              alt="Twitter" 
              width={24} 
              height={24}
              className="rounded"
            />
          </a>
          <a href="#" className="hover:opacity-80 transition-opacity">
            <Image 
              src="/images/linkedin.webp" 
              alt="LinkedIn" 
              width={24} 
              height={24}
              className="rounded"
            />
          </a>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
