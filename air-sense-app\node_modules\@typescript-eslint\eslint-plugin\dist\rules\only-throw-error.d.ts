import type { TypeOrValueSpecifier } from '../util';
export type MessageIds = 'object' | 'undef';
export type Options = [
    {
        allow?: TypeOrValueSpecifier[];
        allowRethrowing?: boolean;
        allowThrowingAny?: boolean;
        allowThrowingUnknown?: boolean;
    }
];
declare const _default: import("@typescript-eslint/utils/ts-eslint").RuleModule<MessageIds, Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
export default _default;
//# sourceMappingURL=only-throw-error.d.ts.map