const baseAirQualityStandards = {
    AQI: {
        good: { min: 0, max: 66, color: 'good' },
        moderate: { min: 67, max: 99, color: 'moderate' },
        poor: { min: 100, max: Infinity, color: 'poor' }
    },
    Temperature: { 
        poor: { min: -Infinity, max: 18, unit: '°C' },
        moderate: { min: 18, max: 21.8, unit: '°C' },
        good: { min: 21.8, max: 26.10, unit: '°C' },
        moderate2: { min: 26.1, max: 30, unit: '°C' },
        poor2: { min: 30, max: Infinity, unit: '°C' }
    },
    Humidity: {
        poor: { min: -Infinity, max: 20, unit: '%' },
        moderate: { min: 20, max: 30, unit: '%' },
        good: { min: 30, max: 60, unit: '%' },
        moderate2: { min: 60, max: 70, unit: '%' },
        poor2: { min: 70, max: Infinity, unit: '%' }
    },
    'CO2 Level': {
        good: { min: 400, max: 800, unit: 'ppm' },
        moderate: { min: 800, max: 1200, unit: 'ppm' },
        poor: { min: 1200, max: Infinity, unit: 'ppm' }
    },
    'PM2.5': {
        good: { min: 0, max: 100, unit: 'µg/m³' },
        moderate: { min: 100, max: 125, unit: 'µg/m³' },
        poor: { min: 125, max: Infinity, unit: 'µg/m³' }
    },
    'PM10': {
        good: { min: 0, max: 200, unit: 'µg/m³' },
        moderate: { min: 200, max: 250, unit: 'µg/m³' },
        poor: { min: 250, max: Infinity, unit: 'µg/m³' }
    },
    'Gas Resistance': {
        good: { min: 50, max: Infinity, unit: 'kΩ' },
        moderate: { min: 10, max: 50, unit: 'kΩ' },
        poor: { min: 0, max: 10, unit:'kΩ' }
    },
    'Pressure': {
        good: { min: 980, max: 1020, unit: 'hPa' },
        moderate: { min: 960, max: 980, unit: 'hPa' },
        poor: { min: -Infinity, max: 960, unit: 'hPa' }
    },
    'Nitrogen Dioxide (NO2)': {
        good: { min: 0, max: 110, unit: 'ppb' },
        moderate: { min: 110, max: 130, unit: 'ppb' },
        poor: { min: 130, max: Infinity, unit: 'ppb' }
    },
    'Ozone (O3)': {
        good: { min: 0, max: 100, unit: 'ppb' },
        moderate: { min: 100, max: 120, unit: 'ppb' },
        poor: { min: 120, max: Infinity, unit: 'ppb' }
    }
};

// Global variables
let globalChart = null;
let currentMetrics = {};
let updateInterval = null;

// Air quality standards reference
const airQualityStandards = {
    defaultLocation: { ...baseAirQualityStandards }
};

// Initialize UI on page load
document.addEventListener("DOMContentLoaded", function () {
    initializeLocationDropdown();
    initializeDashboard();
    startRealTimeUpdates();
});

function initializeLocationDropdown() {
    const fixedDropdown = document.getElementById("fixedLocationDropdown");
    const homeIcon = document.getElementById("comparisonHomeBtn");

    // Show saved location in disabled dropdown
    const savedLocation = localStorage.getItem("userLocation");
    if (fixedDropdown) {
        if (savedLocation) {
            fixedDropdown.innerHTML = `<option selected>${savedLocation}</option>`;
        } else {
            fixedDropdown.innerHTML = `<option selected>Select your location</option>`;
        }
    }

    // On home icon click, clear location and redirect
    if (homeIcon) {
        homeIcon.addEventListener("click", function () {
            localStorage.removeItem("userLocation");
            window.location.href = "home2.html";
        });
    }
}

function calculateAQI(metrics) {
    const breakpoints = {
        pm25: [
            {low: 0, high: 12, aqi: {low: 0, high: 50}},
            {low: 12.1, high: 35.4, aqi: {low: 51, high: 100}},
            {low: 35.5, high: 55.4, aqi: {low: 101, high: 150}},
            {low: 55.5, high: 150.4, aqi: {low: 151, high: 200}},
            {low: 150.5, high: 250.4, aqi: {low: 201, high: 300}}
        ],
        co2: [
            {low: 400, high: 600, aqi: {low: 0, high: 50}},
            {low: 601, high: 800, aqi: {low: 51, high: 100}},
            {low: 801, high: 1200, aqi: {low: 101, high: 150}},
            {low: 1201, high: 1600, aqi: {low: 151, high: 200}},
            {low: 1601, high: 2000, aqi: {low: 201, high: 300}}
        ],
        temperature: [
            {low: 22, high: 24, aqi: {low: 0, high: 50}},
            {low: 24.1, high: 26, aqi: {low: 51, high: 100}},
            {low: 26.1, high: 28, aqi: {low: 101, high: 150}}
        ]
    };

    function calculateMetricAQI(value, metricBreakpoints) {
        const matchedBreakpoint = metricBreakpoints.find(bp => 
            value >= bp.low && value <= bp.high
        );

        if (!matchedBreakpoint) return null;

        const { low: concLow, high: concHigh } = matchedBreakpoint;
        const { low: aqiLow, high: aqiHigh } = matchedBreakpoint.aqi;

        const aqi = ((aqiHigh - aqiLow) / (concHigh - concLow)) * (value - concLow) + aqiLow;
        return Math.round(aqi);
    }

    const aqiValues = [
        calculateMetricAQI(metrics.pm25, breakpoints.pm25),
        calculateMetricAQI(metrics.co2, breakpoints.co2),
        calculateMetricAQI(metrics.temperature, breakpoints.temperature)
    ].filter(val => val !== null);

    return aqiValues.length > 0 
        ? Math.round(aqiValues.reduce((a, b) => a + b, 0) / aqiValues.length)
        : 50;
}

function generateMockValue(metric) {
    const standards = airQualityStandards.defaultLocation[metric];
    let value;
    
    switch(metric) {
        case 'Gas Resistance':
            value = 20 + (Math.random() * 80);
            break;
        case 'Temperature':
            value = 18 + (Math.random() * 15);
            break;
        case 'Humidity':
            value = 25 + (Math.random() * 50);
            break;
        case 'CO2 Level':
            value = 400 + (Math.random() * 1200);
            break;
        case 'PM2.5':
            value = Math.random() * 150;
            break;
        case 'PM10':
            value = Math.random() * 280;
            break;
        case 'Pressure':
            value = 970 + (Math.random() * 60);
            break;
        case 'Nitrogen Dioxide (NO2)':
            value = Math.floor(50 + (Math.random() * 100));
            break;
        case 'Ozone (O3)':
            value = Math.floor(40 + (Math.random() * 100));
            break;
        default:
            value = 50 + (Math.random() * 50);
    }

    if (isNaN(value) || !isFinite(value) || value === 0) {
        return 50;
    }

    if (metric === 'Nitrogen Dioxide (NO2)' || metric === 'Ozone (O3)') {
        return Math.floor(value);
    }
    
    return Number(value.toFixed(1));
}

function getQualityLevel(value, standards) {
    for (const level in standards) {
        const range = standards[level];
        if (value >= range.min && value < range.max) {
            return {
                level: level.replace(/\d+$/, ''),
                unit: range.unit
            };
        }
    }
    return {
        level: 'Poor',
        unit: standards.good ? standards.good.unit : ''
    };
}

function updateAQIDisplay() {
    const pm25 = currentMetrics['PM2.5'] || generateMockValue('PM2.5');
    const co2 = currentMetrics['CO2 Level'] || generateMockValue('CO2 Level');
    const temperature = currentMetrics['Temperature'] || generateMockValue('Temperature');
    
    const mockMetrics = {
        pm25: pm25,
        co2: co2,
        temperature: temperature
    };

    const aqi = calculateAQI(mockMetrics);
    const aqiElement = document.querySelector('.aqi-card .value');
    const aqiStatusElement = document.querySelector('.aqi-card .status');
    const aqiGaugeFill = document.querySelector('.aqi-card .gauge-fill');
    
    if (aqiElement) {
        aqiElement.textContent = aqi;
    }

    const standards = baseAirQualityStandards.AQI;
    let status = '';
    let statusClass = '';
    let percentage = 0;

    if (aqi <= standards.good.max) {
        status = 'Good';
        statusClass = 'good';
        percentage = (aqi / standards.good.max) * 33;
    } else if (aqi <= standards.moderate.max) {
        status = 'Moderate';
        statusClass = 'moderate';
        percentage = 33 + ((aqi - standards.good.max) / (standards.moderate.max - standards.good.max)) * 33;
    } else {
        status = 'Poor';
        statusClass = 'poor';
        percentage = 66 + Math.min(((aqi - standards.moderate.max) / 100) * 34, 34);
    }

    if (aqiStatusElement) {
        aqiStatusElement.textContent = status;
        aqiStatusElement.className = `status ${statusClass}`;
    }

    if (aqiGaugeFill) {
        aqiGaugeFill.style.setProperty('--percentage', `${percentage}%`);
    }
}

function initializeDashboard() {
    const metricsGrid = document.querySelector('.metrics-grid');
    if (!metricsGrid) return;

    // Clear existing content
    metricsGrid.innerHTML = '';

    // Create AQI card
    const aqiCard = document.createElement('div');
    aqiCard.className = 'metric-card aqi-card';
    aqiCard.innerHTML = `
        <h3>Air Quality Index (AQI)</h3>
        <div class="gauge-container aqi-gauge">
            <div class="gauge">
                <div class="gauge-fill"></div>
                <div class="gauge-center">
                    <div class="value-display">
                        <div class="value">--</div>
                        <div class="unit">AQI</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="status">Loading...</div>
    `;
    metricsGrid.appendChild(aqiCard);

    const metrics = [
        'Temperature', 'Humidity', 'PM2.5', 'PM10', 'CO2 Level', 
        'Pressure', 'Gas Resistance', 
        'Nitrogen Dioxide (NO2)', 'Ozone (O3)'
    ];

    metrics.forEach((metric, index) => {
        const metricCard = createMetricCard(metric, index + 1);
        metricsGrid.appendChild(metricCard);
    });

    initializeCharts();
    updateAllMetrics();
}

function createMetricCard(metric, index) {
    const card = document.createElement('div');
    card.className = 'metric-card';
    card.setAttribute('data-metric', metric);
    
    const mockValue = generateMockValue(metric);
    const standards = airQualityStandards.defaultLocation[metric];
    const qualityInfo = getQualityLevel(mockValue, standards);
    
    currentMetrics[metric] = mockValue;

    card.innerHTML = `
        <h3>${metric}</h3>
        <div class="gauge-container">
            <canvas id="gauge${index}" width="180" height="180"></canvas>
            <div class="value-display">
                <div class="value">${mockValue.toFixed(1)}</div>
                <div class="unit">${qualityInfo.unit}</div>
            </div>
        </div>
        <div class="status ${qualityInfo.level.toLowerCase()}">${qualityInfo.level}</div>
    `;

    return card;
}

function determineColor(value, metric) {
    const standards = airQualityStandards.defaultLocation[metric];
    
    if (metric === 'Gas Resistance') {
        if (value >= 50) return '#34A853';
        if (value >= 10 && value < 50) return '#FBBC05';
        return '#EA4335';
    }

    for (const [level, range] of Object.entries(standards)) {
        if (value >= range.min && value < range.max) {
            switch(level.replace(/\d+$/, '')) {
                case 'good': return '#34A853';
                case 'moderate': return '#FBBC05';
                case 'poor': return '#EA4335';
            }
        }
    }
    
    return '#EA4335';
}

function initializeCharts() {
    initializeGaugeCharts();
    initializeTrendsChart();
}

function initializeGaugeCharts() {
    const metrics = [
        'Temperature', 'Humidity', 'PM2.5', 'PM10', 'CO2 Level', 
        'Pressure', 'Gas Resistance', 
        'Nitrogen Dioxide (NO2)', 'Ozone (O3)'
    ];

    metrics.forEach((metric, index) => {
        const canvas = document.getElementById(`gauge${index + 1}`);
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        const value = currentMetrics[metric] || generateMockValue(metric);
        const color = determineColor(value, metric);

        new Chart(ctx, {
            type: 'doughnut',
            data: {
                datasets: [{
                    data: [value, 100 - value],
                    backgroundColor: [color, '#f5f5f5'],
                    borderWidth: 0
                }]
            },
            options: {
                circumference: 360,
                rotation: 270,
                cutout: '75%',
                plugins: {
                    tooltip: { enabled: false },
                    legend: { display: false }
                },
                animation: {
                    animateRotate: true,
                    animateScale: true
                }
            }
        });
    });
}

function updateAllMetrics() {
    const metrics = [
        'Temperature', 'Humidity', 'PM2.5', 'PM10', 'CO2 Level', 
        'Pressure', 'Gas Resistance', 
        'Nitrogen Dioxide (NO2)', 'Ozone (O3)'
    ];

    metrics.forEach((metric, index) => {
        const newValue = generateMockValue(metric);
        currentMetrics[metric] = newValue;
        updateMetricDisplay(metric, newValue, index + 1);
    });

    updateAQIDisplay();
}

function updateMetricDisplay(metric, value, index) {
    const card = document.querySelector(`[data-metric="${metric}"]`);
    if (!card) return;

    const valueElement = card.querySelector('.value');
    const statusElement = card.querySelector('.status');
    const canvas = document.getElementById(`gauge${index}`);

    if (valueElement) {
        valueElement.textContent = (metric === 'Nitrogen Dioxide (NO2)' || metric === 'Ozone (O3)') 
            ? value.toString() 
            : value.toFixed(1);
    }

    const standards = airQualityStandards.defaultLocation[metric];
    const qualityInfo = getQualityLevel(value, standards);
    
    if (statusElement) {
        statusElement.textContent = qualityInfo.level;
        statusElement.className = `status ${qualityInfo.level.toLowerCase()}`;
    }

    // Update gauge chart
    if (canvas) {
        const ctx = canvas.getContext('2d');
        const color = determineColor(value, metric);
        
        // Destroy existing chart and create new one
        Chart.getChart(canvas)?.destroy();
        
        new Chart(ctx, {
            type: 'doughnut',
            data: {
                datasets: [{
                    data: [value, 100 - value],
                    backgroundColor: [color, '#f5f5f5'],
                    borderWidth: 0
                }]
            },
            options: {
                circumference: 360,
                rotation: 270,
                cutout: '75%',
                plugins: {
                    tooltip: { enabled: false },
                    legend: { display: false }
                },
                animation: {
                    animateRotate: true,
                    animateScale: true
                }
            }
        });
    }
}

function initializeTrendsChart() {
    const ctx = document.getElementById('trendsChart');
    if (!ctx) return;

    const dateFrom = document.getElementById('dateFrom');
    const dateTo = document.getElementById('dateTo');
    const metricSelect = document.getElementById('metricSelect');
    const updateChartButton = document.getElementById('updateChart');

    if (!dateFrom || !dateTo || !metricSelect || !updateChartButton) return;

    const metricConfigs = {
        'Temperature': { 
            color: '#e74c3c', 
            unit: '°C',
            label: 'Temperature'
        },
        'Humidity': { 
            color: '#3498db', 
            unit: '%',
            label: 'Humidity'
        },
        'PM2.5': { 
            color: '#34495e', 
            unit: 'µg/m³',
            label: 'PM2.5'
        },
        'PM10': { 
            color: '#ee58cb', 
            unit: 'µg/m³',
            label: 'PM10.0'
        },
        'CO2 Level': { 
            color: '#f1c40f', 
            unit: 'ppm',
            label: 'CO₂ Level'
        },
        'Pressure': { 
            color: '#9b59b6', 
            unit: 'hPa',
            label: 'Pressure'
        },
        'Gas Resistance': { 
            color: '#1abc9c', 
            unit: 'kΩ',
            label: 'Gas Resistance'
        },
        'Nitrogen Dioxide (NO2)': { 
            color: '#e67e22', 
            unit: 'ppb',
            label: 'Nitrogen Dioxide'
        },
        'Ozone (O3)': { 
            color: '#95a5a6', 
            unit: 'ppb',
            label: 'Ozone'
        }
    };

    // Set default date range
    const today = new Date();
    const sevenDaysAgo = new Date(today);
    sevenDaysAgo.setDate(today.getDate() - 7);
    dateFrom.value = sevenDaysAgo.toISOString().split('T')[0];
    dateTo.value = today.toISOString().split('T')[0];

    function generateMockTrendsData(metric, fromDate, toDate) {
        const dates = [];
        const values = [];
        const currentDate = new Date(fromDate);
        const endDate = new Date(toDate);

        while (currentDate <= endDate) {
            dates.push(currentDate.toISOString().split('T')[0]);
            const value = generateMockValue(metric);
            values.push(value);
            currentDate.setDate(currentDate.getDate() + 1);
        }

        return { dates, values };
    }

    function updateChart() {
        const metric = metricSelect.value;
        const fromDate = dateFrom.value;
        const toDate = dateTo.value;
        
        if (!fromDate || !toDate) {
            alert('Please select both start and end dates');
            return;
        }

        const metricConfig = metricConfigs[metric];
        if (!metricConfig) return;

        const { dates, values } = generateMockTrendsData(metric, fromDate, toDate);

        // Destroy existing chart
        if (globalChart) {
            globalChart.destroy();
        }

        // Create new chart
        globalChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: dates,
                datasets: [{
                    label: metricConfig.label,
                    data: values,
                    borderColor: metricConfig.color,
                    backgroundColor: `${metricConfig.color}33`,
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: false,
                        title: {
                            display: true,
                            text: `${metricConfig.label} (${metricConfig.unit})`,
                            font: {
                                size: 14
                            }
                        },
                        ticks: {
                            font: {
                                size: 12
                            }
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Date',
                            font: {
                                size: 14
                            }
                        },
                        ticks: {
                            font: {
                                size: 12
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        labels: {
                            font: {
                                size: 14
                            }
                        }
                    }
                }
            }
        });
    }

    updateChartButton.addEventListener('click', updateChart);
    
    // Initial chart load
    setTimeout(updateChart, 100);
}

function startRealTimeUpdates() {
    // Update metrics every 5 seconds
    updateInterval = setInterval(() => {
        updateAllMetrics();
    }, 5000);
}

function stopRealTimeUpdates() {
    if (updateInterval) {
        clearInterval(updateInterval);
        updateInterval = null;
    }
}

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    stopRealTimeUpdates();
});