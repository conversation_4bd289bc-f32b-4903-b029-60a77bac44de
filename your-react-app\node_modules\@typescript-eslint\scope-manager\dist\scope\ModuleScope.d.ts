import type { TSESTree } from '@typescript-eslint/types';
import type { ScopeManager } from '../ScopeManager';
import type { Scope } from './Scope';
import { ScopeBase } from './ScopeBase';
import { ScopeType } from './ScopeType';
declare class ModuleScope extends ScopeBase<ScopeType.module, TSESTree.Program, Scope> {
    constructor(scopeManager: ScopeManager, upperScope: ModuleScope['upper'], block: ModuleScope['block']);
}
export { ModuleScope };
//# sourceMappingURL=ModuleScope.d.ts.map