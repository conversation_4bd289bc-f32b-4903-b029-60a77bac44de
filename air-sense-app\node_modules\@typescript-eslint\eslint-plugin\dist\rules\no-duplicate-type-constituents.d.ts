export type Options = [
    {
        ignoreIntersections?: boolean;
        ignoreUnions?: boolean;
    }
];
export type MessageIds = 'duplicate' | 'unnecessary';
declare const _default: import("@typescript-eslint/utils/ts-eslint").RuleModule<MessageIds, Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
export default _default;
//# sourceMappingURL=no-duplicate-type-constituents.d.ts.map