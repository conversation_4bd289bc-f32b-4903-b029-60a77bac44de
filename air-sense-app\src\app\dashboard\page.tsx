'use client';

import React, { useState, useEffect } from 'react';
import Layout from '@/components/Layout';

// Air quality standards
const airQualityStandards = {
  Temperature: { 
    poor: { min: -Infinity, max: 18, unit: '°C' },
    moderate: { min: 18, max: 21.8, unit: '°C' },
    good: { min: 21.8, max: 26.10, unit: '°C' },
    moderate2: { min: 26.1, max: 30, unit: '°C' },
    poor2: { min: 30, max: Infinity, unit: '°C' }
  },
  Humidity: {
    poor: { min: -Infinity, max: 20, unit: '%' },
    moderate: { min: 20, max: 30, unit: '%' },
    good: { min: 30, max: 60, unit: '%' },
    moderate2: { min: 60, max: 70, unit: '%' },
    poor2: { min: 70, max: Infinity, unit: '%' }
  },
  'CO2 Level': {
    good: { min: 400, max: 800, unit: 'ppm' },
    moderate: { min: 800, max: 1200, unit: 'ppm' },
    poor: { min: 1200, max: Infinity, unit: 'ppm' }
  },
  'PM2.5': {
    good: { min: 0, max: 100, unit: 'µg/m³' },
    moderate: { min: 100, max: 125, unit: 'µg/m³' },
    poor: { min: 125, max: Infinity, unit: 'µg/m³' }
  },
  'PM10': {
    good: { min: 0, max: 200, unit: 'µg/m³' },
    moderate: { min: 200, max: 250, unit: 'µg/m³' },
    poor: { min: 250, max: Infinity, unit: 'µg/m³' }
  },
  'Gas Resistance': {
    good: { min: 50, max: Infinity, unit: 'kΩ' },
    moderate: { min: 10, max: 50, unit: 'kΩ' },
    poor: { min: 0, max: 10, unit:'kΩ' }
  },
  'Pressure': {
    good: { min: 980, max: 1020, unit: 'hPa' },
    moderate: { min: 960, max: 980, unit: 'hPa' },
    poor: { min: -Infinity, max: 960, unit: 'hPa' }
  },
  'Nitrogen Dioxide (NO2)': {
    good: { min: 0, max: 110, unit: 'ppb' },
    moderate: { min: 110, max: 130, unit: 'ppb' },
    poor: { min: 130, max: Infinity, unit: 'ppb' }
  },
  'Ozone (O3)': {
    good: { min: 0, max: 100, unit: 'ppb' },
    moderate: { min: 100, max: 120, unit: 'ppb' },
    poor: { min: 120, max: Infinity, unit: 'ppb' }
  }
};

// Mock data generator
const generateMockData = () => {
  return {
    Temperature: Math.round((Math.random() * 15 + 18) * 10) / 10,
    Humidity: Math.round(Math.random() * 40 + 30),
    'PM2.5': Math.round(Math.random() * 150),
    'PM10': Math.round(Math.random() * 300),
    'Nitrogen Dioxide (NO2)': Math.round(Math.random() * 150),
    'Ozone (O3)': Math.round(Math.random() * 140),
    'CO2 Level': Math.round(Math.random() * 800 + 400),
    'Pressure': Math.round(Math.random() * 60 + 960),
    'Gas Resistance': Math.round(Math.random() * 90 + 10)
  };
};

const getQualityStatus = (metric: string, value: number) => {
  const standards = airQualityStandards[metric as keyof typeof airQualityStandards];
  if (!standards) return 'moderate';

  for (const [status, range] of Object.entries(standards)) {
    if (status.includes('2')) continue; // Skip duplicate ranges
    if (value >= range.min && value <= range.max) {
      return status;
    }
  }
  return 'moderate';
};

const getStatusColor = (status: string) => {
  switch (status) {
    case 'good': return 'text-green-600 bg-green-100';
    case 'moderate': return 'text-yellow-600 bg-yellow-100';
    case 'poor': return 'text-red-600 bg-red-100';
    default: return 'text-gray-600 bg-gray-100';
  }
};

export default function Dashboard() {
  const [selectedLocation, setSelectedLocation] = useState<string>('');
  const [selectedMetric, setSelectedMetric] = useState<string>('Temperature');
  const [dateFrom, setDateFrom] = useState<string>('');
  const [dateTo, setDateTo] = useState<string>('');
  const [currentData, setCurrentData] = useState<Record<string, number>>({});

  useEffect(() => {
    // Load location from localStorage
    const storedLocation = localStorage.getItem('userLocation');
    if (storedLocation) {
      setSelectedLocation(storedLocation);
    }

    // Generate mock data
    setCurrentData(generateMockData());

    // Set default dates
    const today = new Date();
    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    setDateFrom(weekAgo.toISOString().split('T')[0]);
    setDateTo(today.toISOString().split('T')[0]);
  }, []);

  const metrics = Object.keys(airQualityStandards);

  return (
    <Layout>
      <div className="min-h-screen bg-gray-50">
        <header className="bg-white shadow-sm py-8">
          <div className="max-w-6xl mx-auto px-8">
            <h1 className="text-3xl font-bold text-gray-800 mb-2">
              Welcome to the Smart Indoor Air Quality Monitoring System
            </h1>
            <p className="text-gray-600">
              Track, analyze, and improve indoor air quality with ease
            </p>
          </div>
        </header>

        <main className="max-w-6xl mx-auto px-8 py-8">
          {/* Air Quality Scale */}
          <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
            <h2 className="text-xl font-semibold mb-4">Air Quality Scale</h2>
            <div className="flex items-center gap-4 mb-4">
              <div className="flex-1 h-4 bg-gradient-to-r from-green-400 via-yellow-400 to-red-400 rounded"></div>
            </div>
            <div className="flex justify-between text-sm text-gray-600">
              <span>Good</span>
              <span>Moderate</span>
              <span>Poor</span>
            </div>
          </div>

          {/* Current Location Display */}
          {selectedLocation && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-8">
              <p className="text-blue-800">
                <strong>Current Location:</strong> {selectedLocation.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
              </p>
            </div>
          )}

          {/* Metrics Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            {metrics.map((metric) => {
              const value = currentData[metric] || 0;
              const status = getQualityStatus(metric, value);
              const unit = airQualityStandards[metric as keyof typeof airQualityStandards]?.good?.unit || '';
              
              return (
                <div key={metric} className="bg-white rounded-lg shadow-sm p-6">
                  <h3 className="text-lg font-semibold text-gray-800 mb-2">{metric}</h3>
                  <div className="text-3xl font-bold text-gray-900 mb-2">
                    {value}{unit}
                  </div>
                  <div className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(status)}`}>
                    {status.charAt(0).toUpperCase() + status.slice(1)}
                  </div>
                </div>
              );
            })}
          </div>

          {/* Historical Data Section */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6">
              <h2 className="text-xl font-semibold mb-4 lg:mb-0">Historical Data</h2>
              <div className="flex flex-wrap gap-4">
                <select 
                  value={selectedMetric}
                  onChange={(e) => setSelectedMetric(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {metrics.map(metric => (
                    <option key={metric} value={metric}>{metric}</option>
                  ))}
                </select>
                <input 
                  type="date" 
                  value={dateFrom}
                  onChange={(e) => setDateFrom(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <input 
                  type="date" 
                  value={dateTo}
                  onChange={(e) => setDateTo(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                  Update
                </button>
              </div>
            </div>
            
            <div className="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
              <p className="text-gray-500">Chart visualization would be implemented here with Chart.js</p>
            </div>
          </div>
        </main>
      </div>
    </Layout>
  );
}
