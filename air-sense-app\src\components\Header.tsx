'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { usePathname } from 'next/navigation';

const Header: React.FC = () => {
  const [selectedLocation, setSelectedLocation] = useState<string>('');
  const pathname = usePathname();

  useEffect(() => {
    // Restore selected location from localStorage
    const storedLocation = localStorage.getItem('userLocation');
    if (storedLocation) {
      setSelectedLocation(storedLocation);
    }
  }, []);

  const handleLocationChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const location = event.target.value;
    setSelectedLocation(location);
    localStorage.setItem('userLocation', location);
  };

  const handleHomeClick = () => {
    localStorage.removeItem('userLocation');
    setSelectedLocation('');
  };

  const isActive = (path: string) => pathname === path;

  return (
    <header>
      <nav className="bg-white px-8 py-4 flex items-center justify-between shadow-sm min-h-20">
        <div className="flex items-center">
          <div className="flex items-center">
            <Image 
              src="/images/logo.jpg" 
              alt="AirGuard Logo" 
              width={60} 
              height={60} 
              className="rounded-lg"
            />
          </div>
        </div>
        
        <div className="flex-1 flex justify-center gap-8">
          <Link 
            href="/" 
            className={`text-gray-700 hover:text-blue-600 transition-colors ${
              isActive('/') ? 'text-blue-600 font-semibold' : ''
            }`}
          >
            Home
          </Link>
          <Link 
            href="/dashboard" 
            className={`text-gray-700 hover:text-blue-600 transition-colors ${
              isActive('/dashboard') ? 'text-blue-600 font-semibold' : ''
            }`}
          >
            Dashboard
          </Link>
          <Link 
            href="/comparison" 
            className={`text-gray-700 hover:text-blue-600 transition-colors ${
              isActive('/comparison') ? 'text-blue-600 font-semibold' : ''
            }`}
          >
            Comparison
          </Link>
        </div>
        
        <div className="flex-1 flex justify-end items-center gap-4">
          <button 
            onClick={handleHomeClick}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            title="Replace the location"
          >
            <Image 
              src="/images/home.png" 
              alt="Home" 
              width={32} 
              height={32}
            />
          </button>

          <select 
            value={selectedLocation}
            onChange={handleLocationChange}
            className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 cursor-pointer hover:border-gray-400 transition-colors"
            title="Only change the location when changing the air quality condition measurement location"
          >
            <option value="" disabled>Select your location</option>
            <option value="deans-office">Dean's Office</option>
            <option value="basement">Basement</option>
            <option value="lecture-hall">Lecture Hall</option>
          </select>
        </div>
      </nav>
    </header>
  );
};

export default Header;
