{"version": 3, "file": "triple-slash-reference.js", "sourceRoot": "", "sources": ["../../src/rules/triple-slash-reference.ts"], "names": [], "mappings": ";;AACA,oDAA2E;AAC3E,wEAAsE;AAEtE,kCAAqC;AAWrC,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,wBAAwB;IAC9B,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EACT,oFAAoF;YACtF,WAAW,EAAE,aAAa;SAC3B;QACD,QAAQ,EAAE;YACR,oBAAoB,EAClB,iFAAiF;SACpF;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,GAAG,EAAE;wBACH,IAAI,EAAE,QAAQ;wBACd,IAAI,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC;qBAC1B;oBACD,IAAI,EAAE;wBACJ,IAAI,EAAE,QAAQ;wBACd,IAAI,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC;qBAC1B;oBACD,KAAK,EAAE;wBACL,IAAI,EAAE,QAAQ;wBACd,IAAI,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,eAAe,CAAC;qBAC3C;iBACF;gBACD,oBAAoB,EAAE,KAAK;aAC5B;SACF;KACF;IACD,cAAc,EAAE;QACd;YACE,GAAG,EAAE,QAAQ;YACb,IAAI,EAAE,OAAO;YACb,KAAK,EAAE,eAAe;SACvB;KACF;IACD,MAAM,CAAC,OAAO,EAAE,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;QACpC,IAAI,WAAsC,CAAC;QAC3C,MAAM,UAAU,GAAG,IAAA,4BAAa,EAAC,OAAO,CAAC,CAAC;QAC1C,MAAM,UAAU,GAGV,EAAE,CAAC;QAET,SAAS,oBAAoB,CAAC,MAAwB;YACpD,UAAU,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;gBAC7B,IAAI,SAAS,CAAC,UAAU,KAAK,MAAM,CAAC,KAAK,EAAE,CAAC;oBAC1C,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI,EAAE,SAAS,CAAC,OAAO;wBACvB,SAAS,EAAE,sBAAsB;wBACjC,IAAI,EAAE;4BACJ,MAAM,EAAE,SAAS,CAAC,UAAU;yBAC7B;qBACF,CAAC,CAAC;gBACL,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC;QACD,OAAO;YACL,iBAAiB,CAAC,IAAI;gBACpB,IAAI,WAAW,EAAE,CAAC;oBAChB,oBAAoB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACpC,CAAC;YACH,CAAC;YACD,yBAAyB,CAAC,IAAI;gBAC5B,IAAI,WAAW,EAAE,CAAC;oBAChB,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC;oBAEvC,IAAI,SAAS,CAAC,IAAI,KAAK,sBAAc,CAAC,yBAAyB,EAAE,CAAC;wBAChE,oBAAoB,CAAC,SAAS,CAAC,UAA8B,CAAC,CAAC;oBACjE,CAAC;gBACH,CAAC;YACH,CAAC;YACD,OAAO,CAAC,IAAI;gBACV,IAAI,GAAG,KAAK,QAAQ,IAAI,IAAI,KAAK,QAAQ,IAAI,KAAK,KAAK,QAAQ,EAAE,CAAC;oBAChE,OAAO;gBACT,CAAC;gBACD,WAAW,GAAG,IAAI,CAAC;gBACnB,MAAM,eAAe,GACnB,0DAA0D,CAAC;gBAC7D,MAAM,cAAc,GAAG,UAAU,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;gBAEjE,cAAc,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;oBAC/B,IAAI,OAAO,CAAC,IAAI,KAAK,uBAAe,CAAC,IAAI,EAAE,CAAC;wBAC1C,OAAO;oBACT,CAAC;oBACD,MAAM,eAAe,GAAG,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;oBAE5D,IAAI,eAAe,EAAE,CAAC;wBACpB,IACE,CAAC,eAAe,CAAC,CAAC,CAAC,KAAK,OAAO,IAAI,KAAK,KAAK,OAAO,CAAC;4BACrD,CAAC,eAAe,CAAC,CAAC,CAAC,KAAK,MAAM,IAAI,IAAI,KAAK,OAAO,CAAC;4BACnD,CAAC,eAAe,CAAC,CAAC,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,OAAO,CAAC,EACjD,CAAC;4BACD,OAAO,CAAC,MAAM,CAAC;gCACb,IAAI,EAAE,OAAO;gCACb,SAAS,EAAE,sBAAsB;gCACjC,IAAI,EAAE;oCACJ,MAAM,EAAE,eAAe,CAAC,CAAC,CAAC;iCAC3B;6BACF,CAAC,CAAC;4BACH,OAAO;wBACT,CAAC;wBACD,IAAI,eAAe,CAAC,CAAC,CAAC,KAAK,OAAO,IAAI,KAAK,KAAK,eAAe,EAAE,CAAC;4BAChE,UAAU,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,UAAU,EAAE,eAAe,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;wBAC/D,CAAC;oBACH,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}