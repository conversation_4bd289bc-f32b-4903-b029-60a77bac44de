"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib repo
Object.defineProperty(exports, "__esModule", { value: true });
exports.esnext_collection = void 0;
const base_config_1 = require("./base-config");
const es2024_collection_1 = require("./es2024.collection");
exports.esnext_collection = {
    libs: [es2024_collection_1.es2024_collection],
    variables: [
        ['ReadonlySetLike', base_config_1.TYPE],
        ['Set', base_config_1.TYPE],
        ['ReadonlySet', base_config_1.TYPE],
    ],
};
