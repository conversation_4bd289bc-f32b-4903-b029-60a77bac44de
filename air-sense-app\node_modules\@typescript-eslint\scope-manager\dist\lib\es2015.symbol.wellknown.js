"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib repo
Object.defineProperty(exports, "__esModule", { value: true });
exports.es2015_symbol_wellknown = void 0;
const base_config_1 = require("./base-config");
const es2015_symbol_1 = require("./es2015.symbol");
exports.es2015_symbol_wellknown = {
    libs: [es2015_symbol_1.es2015_symbol],
    variables: [
        ['SymbolConstructor', base_config_1.TYPE],
        ['Symbol', base_config_1.TYPE],
        ['Array', base_config_1.TYPE],
        ['ReadonlyArray', base_config_1.TYPE],
        ['Date', base_config_1.TYPE],
        ['Map', base_config_1.TYPE],
        ['WeakMap', base_config_1.TYPE],
        ['Set', base_config_1.TYPE],
        ['WeakSet', base_config_1.TYPE],
        ['JSON', base_config_1.TYPE],
        ['Function', base_config_1.TYPE],
        ['GeneratorFunction', base_config_1.TYPE],
        ['Math', base_config_1.TYPE],
        ['Promise', base_config_1.TYPE],
        ['PromiseConstructor', base_config_1.TYPE],
        ['RegExp', base_config_1.TYPE],
        ['RegExpConstructor', base_config_1.TYPE],
        ['String', base_config_1.TYPE],
        ['ArrayBuffer', base_config_1.TYPE],
        ['DataView', base_config_1.TYPE],
        ['Int8Array', base_config_1.TYPE],
        ['Uint8Array', base_config_1.TYPE],
        ['Uint8ClampedArray', base_config_1.TYPE],
        ['Int16Array', base_config_1.TYPE],
        ['Uint16Array', base_config_1.TYPE],
        ['Int32Array', base_config_1.TYPE],
        ['Uint32Array', base_config_1.TYPE],
        ['Float32Array', base_config_1.TYPE],
        ['Float64Array', base_config_1.TYPE],
        ['ArrayConstructor', base_config_1.TYPE],
        ['MapConstructor', base_config_1.TYPE],
        ['SetConstructor', base_config_1.TYPE],
        ['ArrayBufferConstructor', base_config_1.TYPE],
    ],
};
