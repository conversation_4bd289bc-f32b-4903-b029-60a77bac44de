import React from 'react';
import fbIcon from '../assets/fb.avif';
import twitterIcon from '../assets/twitter.webp';
import linkedinIcon from '../assets/linkedin.webp';

const Footer: React.FC = () => {
  return (
    <div className="footer bg-dark text-white py-4">
      <div className="container">
        <div className="row align-items-center">
          <div className="col-md-4">
            <p className="mb-0">&copy; 2025 Meridian Innovators. All Rights Reserved.</p>
          </div>
          <div className="col-md-4 text-center">
            <div className="footer-links">
              <a href="#" className="text-white text-decoration-none me-3">Privacy Policy</a>
              <a href="#" className="text-white text-decoration-none me-3">Terms of Service</a>
              <a href="#" className="text-white text-decoration-none">Contact Us</a>
            </div>
          </div>
          <div className="col-md-4 text-end">
            <div className="footer-social-icons">
              <a href="#" className="text-white me-2">
                <img src={fbIcon} alt="Facebook" style={{ height: '24px', width: '24px' }} />
              </a>
              <a href="#" className="text-white me-2">
                <img src={twitterIcon} alt="Twitter" style={{ height: '24px', width: '24px' }} />
              </a>
              <a href="#" className="text-white">
                <img src={linkedinIcon} alt="LinkedIn" style={{ height: '24px', width: '24px' }} />
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Footer;
