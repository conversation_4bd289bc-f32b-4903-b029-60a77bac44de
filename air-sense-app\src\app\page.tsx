import Image from "next/image";
import Layout from "@/components/Layout";
import Carousel from "@/components/Carousel";

const carouselItems = [
  {
    title: "Real-Time Monitoring",
    image: "/images/live_air.jpg",
    description: "Stay updated with live air quality readings tailored to your environment."
  },
  {
    title: "Access to Historical Data",
    image: "/images/history.jpg",
    description: "Analyze past data to identify patterns and ensure long-term safety."
  },
  {
    title: "Air Quality Measurement",
    image: "/images/air_index.jpg",
    description: "Continuously measures air quality parameters, including CO2, VOCs, particulate matter, and more."
  },
  {
    title: "Comparison of Air Quality",
    image: "/images/comparison.png",
    description: "Displays graphical comparisons of air quality metrics between two selected locations effectively."
  }
];

export default function Home() {
  return (
    <Layout>
      <section className="hero-section min-h-screen flex flex-col items-center justify-center text-center px-8 py-16">
        <div className="bg-white bg-opacity-90 rounded-lg p-8 max-w-4xl mx-auto">
          <h1 className="text-4xl md:text-5xl font-bold mb-8 text-gray-800">
            Welcome to the Smart Indoor Air Quality Monitoring System
          </h1>

          <div className="mb-8">
            <Image
              src="/images/logo.jpg"
              alt="AirSense Logo"
              width={400}
              height={400}
              className="rounded-3xl mx-auto"
            />
          </div>

          <p className="text-xl text-gray-600 mb-12">
            Track, analyze, and improve indoor air quality with ease
          </p>

          <section className="features-section">
            <h2 className="text-4xl font-bold mb-8 text-gray-800">Our Features</h2>
            <Carousel items={carouselItems} />
          </section>
        </div>
      </section>
    </Layout>
  );
}
