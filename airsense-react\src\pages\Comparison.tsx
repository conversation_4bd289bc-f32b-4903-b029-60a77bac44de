import React, { useState, useEffect } from 'react';
import Layout from '../layouts/Layout';
import CircularGauge from '../components/CircularGauge';
import ComparisonChart from '../components/ComparisonChart';

const locations = [
  { value: 'lectureHall', label: 'Lecture Hall' },
  { value: 'Office', label: "Dean's Office" },
  { value: 'Basement', label: 'Basement' },
  { value: 'electronicLab', label: 'Lab' }
];

const metrics = [
  'Temperature',
  'Humidity', 
  'PM2.5',
  'PM10',
  'CO2 Level',
  'Gas Resistance',
  'Pressure',
  'Nitrogen Dioxide (NO2)',
  'Ozone (O3)'
];

// Air quality standards for different metrics
const baseAirQualityStandards = {
  Temperature: {
    poor: { min: -Infinity, max: 18, unit: '°C' },
    moderate: { min: 18, max: 21.8, unit: '°C' },
    good: { min: 21.8, max: 26.1, unit: '°C' },
    moderate2: { min: 26.1, max: 30, unit: '°C' },
    poor2: { min: 30, max: Infinity, unit: '°C' }
  },
  Humidity: {
    poor: { min: -Infinity, max: 20, unit: '%' },
    moderate: { min: 20, max: 30, unit: '%' },
    good: { min: 30, max: 60, unit: '%' },
    moderate2: { min: 60, max: 70, unit: '%' },
    poor2: { min: 70, max: Infinity, unit: '%' }
  },
  'CO2 Level': {
    good: { min: 400, max: 800, unit: 'ppm' },
    moderate: { min: 800, max: 1200, unit: 'ppm' },
    poor: { min: 1200, max: Infinity, unit: 'ppm' }
  },
  'PM2.5': {
    good: { min: 0, max: 100, unit: 'µg/m³' },
    moderate: { min: 100, max: 125, unit: 'µg/m³' },
    poor: { min: 125, max: Infinity, unit: 'µg/m³' }
  },
  'PM10': {
    good: { min: 0, max: 200, unit: 'µg/m³' },
    moderate: { min: 200, max: 250, unit: 'µg/m³' },
    poor: { min: 250, max: Infinity, unit: 'µg/m³' }
  },
  'Gas Resistance': {
    good: { min: 500, max: 1500, unit: 'Ω' },
    moderate: { min: 1500, max: 2500, unit: 'Ω' },
    poor: { min: 2500, max: Infinity, unit: 'Ω' }
  },
  'Pressure': {
    good: { min: 980, max: 1020, unit: 'hPa' },
    moderate: { min: 960, max: 980, unit: 'hPa' },
    poor: { min: -Infinity, max: 960, unit: 'hPa' }
  },
  'Nitrogen Dioxide (NO2)': {
    good: { min: 0, max: 110, unit: 'ppb' },
    moderate: { min: 110, max: 130, unit: 'ppb' },
    poor: { min: 130, max: Infinity, unit: 'ppb' }
  },
  'Ozone (O3)': {
    good: { min: 0, max: 100, unit: 'ppb' },
    moderate: { min: 100, max: 120, unit: 'ppb' },
    poor: { min: 120, max: Infinity, unit: 'ppb' }
  }
};

// Generate Air Quality Standards for all locations
const airQualityStandards = {
  lectureHall: { ...baseAirQualityStandards },
  Office: { ...baseAirQualityStandards },
  Basement: { ...baseAirQualityStandards },
  electronicLab: { ...baseAirQualityStandards }
};

// Mock data generator
const generateMockData = (location: string, metric: string) => {
  if (!location || !metric) return 0;

  const standards = airQualityStandards[location as keyof typeof airQualityStandards]?.[metric as keyof typeof baseAirQualityStandards];
  if (!standards) return 0;

  const goodRange = standards.good;
  const value = goodRange.min + (Math.random() * (goodRange.max - goodRange.min));
  return Math.round(value * 10) / 10;
};

const getQualityLevel = (value: number, standards: any) => {
  // Iterate through standard levels to determine quality
  for (const level in standards) {
    const range = standards[level];
    if (value >= range.min && value < range.max) {
      return {
        level: level.replace(/\d+$/, ''), // Remove numeric suffixes
        unit: range.unit
      };
    }
  }
  return {
    level: 'poor',
    unit: standards.good?.unit || ''
  };
};

const getQualityStatus = (location: string, metric: string, value: number) => {
  const standards = airQualityStandards[location as keyof typeof airQualityStandards]?.[metric as keyof typeof baseAirQualityStandards];
  if (!standards) return 'moderate';

  const qualityInfo = getQualityLevel(value, standards);
  return qualityInfo.level;
};

const getStatusColor = (status: string) => {
  switch (status) {
    case 'good': return 'success';
    case 'moderate': return 'warning';
    case 'poor': return 'danger';
    default: return 'secondary';
  }
};

// Get max value for gauge based on metric
const getMaxValue = (metric: string) => {
  const maxValues: { [key: string]: number } = {
    'Temperature': 40,
    'Humidity': 100,
    'PM2.5': 300,
    'PM10': 500,
    'CO2 Level': 2000,
    'Gas Resistance': 2000,
    'Pressure': 1050,
    'Nitrogen Dioxide (NO2)': 200,
    'Ozone (O3)': 200
  };
  return maxValues[metric] || 100;
};

const Comparison: React.FC = () => {
  const [location1, setLocation1] = useState<string>('');
  const [location2, setLocation2] = useState<string>('');
  const [date1, setDate1] = useState<string>('');
  const [date2, setDate2] = useState<string>('');
  const [metric1, setMetric1] = useState<string>('Temperature');
  const [metric2, setMetric2] = useState<string>('Temperature');
  const [data1, setData1] = useState<number>(0);
  const [data2, setData2] = useState<number>(0);
  const [validationMessage, setValidationMessage] = useState<string>('');

  useEffect(() => {
    const today = new Date().toISOString().split('T')[0];
    setDate1(today);
    setDate2(today);
  }, []);

  useEffect(() => {
    if (location1 && metric1) {
      setData1(generateMockData(location1, metric1));
    }
  }, [location1, metric1, date1]);

  useEffect(() => {
    if (location2 && metric2) {
      setData2(generateMockData(location2, metric2));
    }
  }, [location2, metric2, date2]);

  useEffect(() => {
    if (location1 && location2 && location1 === location2) {
      setValidationMessage('Please select different locations for comparison');
    } else {
      setValidationMessage('');
    }
  }, [location1, location2]);

  const status1 = location1 ? getQualityStatus(location1, metric1, data1) : 'moderate';
  const status2 = location2 ? getQualityStatus(location2, metric2, data2) : 'moderate';

  const getUnit = (location: string, metric: string) => {
    const standards = airQualityStandards[location as keyof typeof airQualityStandards]?.[metric as keyof typeof baseAirQualityStandards];
    return standards?.good?.unit || '';
  };

  const getComparisonText = () => {
    if (!location1 || !location2) {
      return 'Select locations and metrics to view comparison';
    }

    const loc1Name = locations.find(l => l.value === location1)?.label || location1;
    const loc2Name = locations.find(l => l.value === location2)?.label || location2;
    const unit1 = getUnit(location1, metric1);
    const unit2 = getUnit(location2, metric2);

    return `Comparing ${metric1} at ${loc1Name} (${data1}${unit1}) with ${metric2} at ${loc2Name} (${data2}${unit2}).
            ${loc1Name} shows ${status1} air quality while ${loc2Name} shows ${status2} air quality.`;
  };

  return (
    <Layout>
      <header className="welcome-section">
        <h1>Welcome to the Smart Indoor Air Quality Monitoring System</h1>
        <p className="subtitle">Track, analyze, and improve indoor air quality with ease</p>
      </header>

      <main className="container">
        <h2 className="mb-4">Select your Locations</h2>
        
        {validationMessage && (
          <div className="alert alert-danger mb-4">
            {validationMessage}
          </div>
        )}

        <div className="comparison-grid">
          {/* Location 1 */}
          <div className="location-card">
            <h3>Location 1</h3>
                
            <div className="mb-3">
              <label className="form-label">Location:</label>
              <select
                value={location1}
                onChange={(e) => setLocation1(e.target.value)}
                className="form-select"
              >
                <option value="">Select Location</option>
                {locations.map(loc => (
                  <option key={loc.value} value={loc.value}>{loc.label}</option>
                ))}
              </select>
            </div>

            <div className="mb-3">
              <label className="form-label">Date:</label>
              <input
                type="date"
                value={date1}
                onChange={(e) => setDate1(e.target.value)}
                className="form-control"
              />
            </div>

            <div className="mb-3">
              <label className="form-label">Metrics:</label>
              <select
                value={metric1}
                onChange={(e) => setMetric1(e.target.value)}
                className="form-select"
              >
                {metrics.map(metric => (
                  <option key={metric} value={metric}>{metric}</option>
                ))}
              </select>
            </div>

            <div className="gauge-container">
              {location1 ? (
                <CircularGauge
                  value={data1}
                  maxValue={getMaxValue(metric1)}
                  unit={getUnit(location1, metric1)}
                  title=""
                  status={status1 as 'good' | 'moderate' | 'poor'}
                  size={180}
                />
              ) : (
                <div className="text-muted text-center">Select location to view data</div>
              )}
            </div>

            <div className="status">
              {location1 ? `Data from ${locations.find(l => l.value === location1)?.label}` : 'Select location to view data'}
            </div>

            <div className="legend">
              <span className="legend-item">
                <span className="dot bg-success rounded-circle d-inline-block me-1" style={{width: '10px', height: '10px'}}></span>
                Good
              </span>
              <span className="legend-item">
                <span className="dot bg-warning rounded-circle d-inline-block me-1" style={{width: '10px', height: '10px'}}></span>
                Moderate
              </span>
              <span className="legend-item">
                <span className="dot bg-danger rounded-circle d-inline-block me-1" style={{width: '10px', height: '10px'}}></span>
                Poor
              </span>
            </div>
          </div>

          {/* Location 2 */}
          <div className="location-card">
            <h3>Location 2</h3>

            <div className="mb-3">
              <label className="form-label">Location:</label>
              <select
                value={location2}
                onChange={(e) => setLocation2(e.target.value)}
                className="form-select"
              >
                <option value="">Select Location</option>
                {locations.map(loc => (
                  <option key={loc.value} value={loc.value}>{loc.label}</option>
                ))}
              </select>
            </div>

            <div className="mb-3">
              <label className="form-label">Date:</label>
              <input
                type="date"
                value={date2}
                onChange={(e) => setDate2(e.target.value)}
                className="form-control"
              />
            </div>

            <div className="mb-3">
              <label className="form-label">Metrics:</label>
              <select
                value={metric2}
                onChange={(e) => setMetric2(e.target.value)}
                className="form-select"
              >
                {metrics.map(metric => (
                  <option key={metric} value={metric}>{metric}</option>
                ))}
              </select>
            </div>

            <div className="gauge-container">
              {location2 ? (
                <CircularGauge
                  value={data2}
                  maxValue={getMaxValue(metric2)}
                  unit={getUnit(location2, metric2)}
                  title=""
                  status={status2 as 'good' | 'moderate' | 'poor'}
                  size={180}
                />
              ) : (
                <div className="text-muted text-center">Select location to view data</div>
              )}
            </div>

            <div className="status">
              {location2 ? `Data from ${locations.find(l => l.value === location2)?.label}` : 'Select location to view data'}
            </div>

            <div className="legend">
              <span className="legend-item">
                <span className="dot bg-success rounded-circle d-inline-block me-1" style={{width: '10px', height: '10px'}}></span>
                Good
              </span>
              <span className="legend-item">
                <span className="dot bg-warning rounded-circle d-inline-block me-1" style={{width: '10px', height: '10px'}}></span>
                Moderate
              </span>
              <span className="legend-item">
                <span className="dot bg-danger rounded-circle d-inline-block me-1" style={{width: '10px', height: '10px'}}></span>
                Poor
              </span>
            </div>
          </div>
        </div>

        {/* Error Message Display */}
        {validationMessage && (
          <div className="chart-error alert alert-warning text-center mb-4">
            <i className="bi bi-exclamation-triangle me-2"></i>
            {validationMessage}
          </div>
        )}

        {/* Only show comparison content when no validation errors */}
        {!validationMessage && location1 && location2 && (
          <>
            {/* Comparison Summary */}
            <div className="comparison-summary">
              <h3>Comparison Summary</h3>
              <div className="comparison-text">
                {getComparisonText()}
              </div>
            </div>

            {/* Daily Distribution Chart */}
            <div className="daily-distribution">
              <h3>Daily Distribution</h3>
              <div className="chart-container bg-light p-4 rounded" style={{ height: '350px' }}>
                <ComparisonChart
                  location1={location1}
                  location2={location2}
                  metric1={metric1}
                  metric2={metric2}
                  height={300}
                />
              </div>
            </div>
          </>
        )}
      </main>
    </Layout>
  );
};

export default Comparison;
