import React, { useState, useEffect, useRef } from 'react';
import Layout from '../layouts/Layout';
import { Chart, ChartConfiguration, registerables } from 'chart.js';
import '../styles/Comparison.css';

Chart.register(...registerables);

// Base Air Quality Standards Template
const baseAirQualityStandards = {
  Temperature: { 
    poor: { min: -Infinity, max: 18, unit: '°C' },
    moderate: { min: 18, max: 21.8, unit: '°C' },
    good: { min: 21.8, max: 26.1, unit: '°C' },
    moderate2: { min: 26.1, max: 30, unit: '°C' },
    poor2: { min: 30, max: Infinity, unit: '°C' }
  },
  Humidity: {
    poor: { min: -Infinity, max: 20, unit: '%' },
    moderate: { min: 20, max: 30, unit: '%' },
    good: { min: 30, max: 60, unit: '%' },
    moderate2: { min: 60, max: 70, unit: '%' },
    poor2: { min: 70, max: Infinity, unit: '%' }
  },
  'CO2 Level': {
    good: { min: 400, max: 800, unit: 'ppm' },
    moderate: { min: 800, max: 1200, unit: 'ppm' },
    poor: { min: 1200, max: Infinity, unit: 'ppm' }
  },
  'PM2.5': {
    good: { min: 0, max: 100, unit: 'µg/m³' },
    moderate: { min: 100, max: 125, unit: 'µg/m³' },
    poor: { min: 125, max: Infinity, unit: 'µg/m³' }
  },
  'PM10': {
    good: { min: 0, max: 200, unit: 'µg/m³' },
    moderate: { min: 200, max: 250, unit: 'µg/m³' },
    poor: { min: 250, max: Infinity, unit: 'µg/m³' }
  },
  'Gas Resistance': {
    good: { min: 500, max: 1500, unit: 'Ω' },
    moderate: { min: 1500, max: 2500, unit: 'Ω' },
    poor: { min: 2500, max: Infinity, unit: 'Ω' }
  },
  'Pressure': {
    good: { min: 980, max: 1020, unit: 'hPa' },
    moderate: { min: 960, max: 980, unit: 'hPa' },
    poor: { min: -Infinity, max: 960, unit: 'hPa' }
  },
  'Nitrogen Dioxide (NO2)': {
    good: { min: 0, max: 110, unit: 'ppb' },
    moderate: { min: 110, max: 130, unit: 'ppb' },
    hazardous: { min: 130, max: Infinity, unit: 'ppb' }
  },
  'Ozone (O3)': {
    good: { min: 0, max: 100, unit: 'ppb' },
    moderate: { min: 100, max: 120, unit: 'ppm' },
    hazardous: { min: 120, max: Infinity, unit: 'ppm' }
  }
};

// Generate Air Quality Standards for all locations
const airQualityStandards = {
  lectureHall: { ...baseAirQualityStandards },
  Office: { ...baseAirQualityStandards },
  Basement: { ...baseAirQualityStandards },
  electronicLab: { ...baseAirQualityStandards }
};

interface ChartData {
  location1: Array<{ value: number; hour: number }>;
  location2: Array<{ value: number; hour: number }>;
}

const Comparison: React.FC = () => {
  const [location1, setLocation1] = useState('');
  const [location2, setLocation2] = useState('');
  const [metric1, setMetric1] = useState('');
  const [metric2, setMetric2] = useState('');
  const [date1, setDate1] = useState('');
  const [date2, setDate2] = useState('');
  const [validationMessage, setValidationMessage] = useState('');
  const [chartData, setChartData] = useState<ChartData>({ location1: [], location2: [] });
  
  const gauge1Ref = useRef<HTMLCanvasElement>(null);
  const gauge2Ref = useRef<HTMLCanvasElement>(null);
  const distributionChartRef = useRef<HTMLCanvasElement>(null);
  
  const gaugeChartsRef = useRef<{ [key: string]: Chart }>({});
  const distributionChartInstanceRef = useRef<Chart | null>(null);

  useEffect(() => {
    setupDatePickers();
    initializeGauges();
    initializeDistributionChart();

    return () => {
      // Cleanup charts
      Object.values(gaugeChartsRef.current).forEach(chart => chart.destroy());
      if (distributionChartInstanceRef.current) {
        distributionChartInstanceRef.current.destroy();
      }
    };
  }, []);

  useEffect(() => {
    updateData();
  }, [location1, location2, metric1, metric2, date1, date2]);

  const setupDatePickers = () => {
    const today = new Date().toISOString().split('T')[0];
    setDate1(today);
    setDate2(today);
  };

  const initializeGauges = () => {
    const config: ChartConfiguration = {
      type: 'doughnut',
      data: {
        datasets: [{
          data: [0, 100],
          backgroundColor: ['#28a745', '#e9ecef'],
          borderWidth: 0
        }]
      },
      options: {
        circumference: 180,
        rotation: 270,
        cutout: '70%',
        plugins: {
          tooltip: { enabled: false },
          legend: { display: false }
        },
        responsive: true,
        maintainAspectRatio: false
      }
    };

    if (gauge1Ref.current) {
      const ctx1 = gauge1Ref.current.getContext('2d');
      if (ctx1) {
        gaugeChartsRef.current['gauge1'] = new Chart(ctx1, { ...config });
      }
    }

    if (gauge2Ref.current) {
      const ctx2 = gauge2Ref.current.getContext('2d');
      if (ctx2) {
        gaugeChartsRef.current['gauge2'] = new Chart(ctx2, { ...config });
      }
    }
  };

  const initializeDistributionChart = () => {
    if (distributionChartRef.current) {
      const ctx = distributionChartRef.current.getContext('2d');
      if (ctx) {
        distributionChartInstanceRef.current = new Chart(ctx, {
          type: 'line',
          data: {
            labels: [],
            datasets: [
              {
                label: 'Location 1',
                borderColor: '#007bff',
                data: []
              },
              {
                label: 'Location 2',
                borderColor: '#28a745',
                data: []
              }
            ]
          },
          options: {
            responsive: true,
            scales: {
              y: {
                beginAtZero: true
              }
            }
          }
        });
      }
    }
  };

  const getQualityLevel = (value: number, standards: any) => {
    for (const level in standards) {
      const range = standards[level];
      if (value >= range.min && value < range.max) {
        return {
          level: level.replace(/\d+$/, ''),
          unit: range.unit
        };
      }
    }
    return {
      level: 'poor',
      unit: standards.good.unit
    };
  };

  const updateGauge = (index: number, value: number, location: string, metric: string) => {
    const standards = airQualityStandards[location as keyof typeof airQualityStandards][metric];
    const qualityInfo = getQualityLevel(value, standards);
    const gaugeChart = gaugeChartsRef.current[`gauge${index}`];
    
    if (!gaugeChart) return;

    const colors = {
      good: '#28a745',
      moderate: '#ffc107',
      poor: '#dc3545'
    };
    
    const goodRange = standards.good;
    const filledPercentage = Math.min(100, Math.max(0, 
      ((value - goodRange.min) / (goodRange.max - goodRange.min)) * 100
    ));
    
    gaugeChart.data.datasets[0].backgroundColor = [
      colors[qualityInfo.level as keyof typeof colors],
      '#e9ecef'
    ];
    
    gaugeChart.data.datasets[0].data = [filledPercentage, 100 - filledPercentage];
    gaugeChart.update();
    
    // Update value display
    const valueElement = document.getElementById(`gauge${index}-value`);
    if (valueElement) {
      valueElement.textContent = `${value.toFixed(1)}${qualityInfo.unit}`;
    }
    
    // Update status
    const statusElement = document.getElementById(`status${index}`);
    if (statusElement) {
      statusElement.textContent = qualityInfo.level.charAt(0).toUpperCase() + qualityInfo.level.slice(1);
      statusElement.className = `status ${qualityInfo.level}`;
    }
  };

  const generateMockData = (location: string, metric: string, date: string, index: number) => {
    if (!location || !metric) {
      const statusElement = document.getElementById(`status${index}`);
      if (statusElement) {
        statusElement.textContent = "Select location and metric to view data";
      }
      return;
    }

    const standards = airQualityStandards[location as keyof typeof airQualityStandards][metric];
    const goodRange = standards.good;
    const value = goodRange.min + (Math.random() * (goodRange.max - goodRange.min));
    
    updateGauge(index, value, location, metric);
    
    // Generate hourly data for distribution chart
    const hourlyData = Array.from({length: 24}, (_, i) => ({
      value: goodRange.min + (Math.random() * (goodRange.max - goodRange.min)),
      hour: i
    }));
    
    setChartData(prev => ({
      ...prev,
      [`location${index}`]: hourlyData
    }));
  };

  const updateData = () => {
    // Check if all required fields are filled
    if (!location1 || !location2 || !date1 || !date2) {
      clearCharts();
      return;
    }

    // Check for same locations
    if (location1 === location2) {
      showError("Please select different locations for comparison");
      clearCharts();
      return;
    }

    // Check for same dates
    if (date1 === date2) {
      showError("Please select different dates for comparison");
      clearCharts();
      return;
    }

    // Check for different metrics
    if (metric1 !== metric2) {
      showError("Please select the same metric for both locations");
      clearCharts();
      return;
    }

    hideError();
    generateMockData(location1, metric1, date1, 1);
    generateMockData(location2, metric2, date2, 2);
    updateComparison();
    updateDistributionChart();
  };

  const showError = (message: string) => {
    setValidationMessage(message);
    const comparisonText = document.getElementById('comparison-text');
    if (comparisonText) {
      comparisonText.textContent = message;
    }
  };

  const hideError = () => {
    setValidationMessage('');
  };

  const clearCharts = () => {
    const comparisonText = document.getElementById('comparison-text');
    if (comparisonText) {
      comparisonText.textContent = "Select valid comparison parameters to view summary";
    }

    // Clear gauge charts
    Object.values(gaugeChartsRef.current).forEach(gauge => {
      if (gauge) {
        gauge.data.datasets[0].backgroundColor = ['#e9ecef', '#e9ecef'];
        gauge.data.datasets[0].data = [0, 100];
        gauge.update();
      }
    });

    // Clear distribution chart
    if (distributionChartInstanceRef.current) {
      distributionChartInstanceRef.current.data.datasets.forEach(dataset => {
        dataset.data = [];
      });
      distributionChartInstanceRef.current.update();
    }

    // Clear gauge values
    const gauge1Value = document.getElementById('gauge1-value');
    const gauge2Value = document.getElementById('gauge2-value');
    if (gauge1Value) gauge1Value.textContent = '--';
    if (gauge2Value) gauge2Value.textContent = '--';

    // Reset status indicators
    const status1 = document.getElementById('status1');
    const status2 = document.getElementById('status2');
    if (status1) {
      status1.textContent = 'Select location to view data';
      status1.className = 'status';
    }
    if (status2) {
      status2.textContent = 'Select location to view data';
      status2.className = 'status';
    }
  };

  const updateComparison = () => {
    if (!location1 || !location2) {
      const comparisonText = document.getElementById('comparison-text');
      if (comparisonText) {
        comparisonText.textContent = "Select both locations to view comparison";
      }
      return;
    }

    const value1Element = document.getElementById('gauge1-value');
    const value2Element = document.getElementById('gauge2-value');
    const status1Element = document.getElementById('status1');
    const status2Element = document.getElementById('status2');

    if (!value1Element || !value2Element || !status1Element || !status2Element) return;

    const value1 = parseFloat(value1Element.textContent || '0');
    const value2 = parseFloat(value2Element.textContent || '0');
    const quality1 = status1Element.textContent?.toLowerCase() || '';
    const quality2 = status2Element.textContent?.toLowerCase() || '';

    const standards1 = airQualityStandards[location1 as keyof typeof airQualityStandards][metric1];
    const standards2 = airQualityStandards[location2 as keyof typeof airQualityStandards][metric2];

    const comparisonText = `
      ${location1} shows ${value1.toFixed(1)}${standards1.good.unit}
      for ${metric1} (${quality1}), while ${location2} shows
      ${value2.toFixed(1)}${standards2.good.unit}
      for ${metric2} (${quality2}).
    `;

    const comparisonTextElement = document.getElementById('comparison-text');
    if (comparisonTextElement) {
      comparisonTextElement.textContent = comparisonText;
    }
  };

  const updateDistributionChart = () => {
    if (!location1 || !location2 || location1 === location2) return;

    if (distributionChartInstanceRef.current) {
      distributionChartInstanceRef.current.data.labels = Array.from({length: 24}, (_, i) => `${i}:00`);
      distributionChartInstanceRef.current.data.datasets[0].data = chartData.location1.map(d => d.value);
      distributionChartInstanceRef.current.data.datasets[1].data = chartData.location2.map(d => d.value);
      distributionChartInstanceRef.current.data.datasets[0].label = location1;
      distributionChartInstanceRef.current.data.datasets[1].label = location2;

      distributionChartInstanceRef.current.update();
    }
  };

  return (
    <Layout showNavbar={true}>
      <header className="welcome-section">
        <h1>Welcome to the Smart Indoor Air Quality Monitoring System</h1>
        <p className="subtitle">Track, analyze, and improve indoor air quality with ease</p>
      </header>

      <main className="container">
        <h2>Select your Locations</h2>
        
        <div id="validation-message" className="validation-message" style={{
          display: validationMessage ? 'block' : 'none'
        }}>
          {validationMessage}
        </div>

        <div className="comparison-grid">
          {/* Location 1 */}
          <div className="location-card">
            <h3>Location 1</h3>
            <select
              id="location1"
              className="location-select"
              value={location1}
              onChange={(e) => setLocation1(e.target.value)}
            >
              <option value="">Select Location</option>
              <option value="lectureHall">Lecture Hall</option>
              <option value="Office">Dean's Office</option>
              <option value="Basement">Basement</option>
              <option value="electronicLab">Lab</option>
            </select>
            
            <div className="date-picker">
              <label>Date:</label>
              <input
                type="date"
                id="date1"
                value={date1}
                onChange={(e) => setDate1(e.target.value)}
              />
            </div>

            <div className="metrics-select">
              <label>Metrics:</label>
              <select
                id="metrics1"
                value={metric1}
                onChange={(e) => setMetric1(e.target.value)}
              >
                <option value="">Select Metric</option>
                <option value="Temperature">Temperature</option>
                <option value="Humidity">Humidity</option>
                <option value="PM2.5">PM2.5</option>
                <option value="PM10">PM10</option>
                <option value="CO2 Level">Carbon Dioxide CO₂</option>
                <option value="Gas Resistance">Gas Resistance</option>
                <option value="Pressure">Pressure</option>
                <option value="Nitrogen Dioxide (NO2)">Nitrogen Dioxide (NO2)</option>
                <option value="Ozone (O3)">Ozone (O3)</option>
              </select>
            </div>

            <div className="gauge-container">
              <canvas ref={gauge1Ref} id="gauge1" width="200" height="200"></canvas>
              <div id="gauge1-value" className="value-display">--</div>
            </div>

            <div id="status1" className="status">
              Select location to view data
            </div>

            <div className="legend">
              <span className="legend-item">
                <span className="dot good"></span>
                Good
              </span>
              <span className="legend-item">
                <span className="dot moderate"></span>
                Moderate
              </span>
              <span className="legend-item">
                <span className="dot poor"></span>
                Poor
              </span>
            </div>
          </div>

          {/* Location 2 */}
          <div className="location-card">
            <h3>Location 2</h3>
            <select
              id="location2"
              className="location-select"
              value={location2}
              onChange={(e) => setLocation2(e.target.value)}
            >
              <option value="">Select Location</option>
              <option value="lectureHall">Lecture Hall</option>
              <option value="Office">Dean's Office</option>
              <option value="Basement">Basement</option>
              <option value="electronicLab">Lab</option>
            </select>

            <div className="date-picker">
              <label>Date:</label>
              <input
                type="date"
                id="date2"
                value={date2}
                onChange={(e) => setDate2(e.target.value)}
              />
            </div>

            <div className="metrics-select">
              <label>Metrics:</label>
              <select
                id="metrics2"
                value={metric2}
                onChange={(e) => setMetric2(e.target.value)}
              >
                <option value="">Select Metric</option>
                <option value="Temperature">Temperature</option>
                <option value="Humidity">Humidity</option>
                <option value="PM2.5">PM2.5</option>
                <option value="PM10">PM10</option>
                <option value="CO2 Level">Carbon Dioxide CO₂</option>
                <option value="Gas Resistance">Gas Resistance</option>
                <option value="Pressure">Pressure</option>
                <option value="Nitrogen Dioxide (NO2)">Nitrogen Dioxide (NO2)</option>
                <option value="Ozone (O3)">Ozone (O3)</option>
              </select>
            </div>

            <div className="gauge-container">
              <canvas ref={gauge2Ref} id="gauge2" width="200" height="200"></canvas>
              <div id="gauge2-value" className="value-display">--</div>
            </div>

            <div id="status2" className="status">
              Select location to view data
            </div>

            <div className="legend">
              <span className="legend-item">
                <span className="dot good"></span>
                Good
              </span>
              <span className="legend-item">
                <span className="dot moderate"></span>
                Moderate
              </span>
              <span className="legend-item">
                <span className="dot poor"></span>
                Poor
              </span>
            </div>
          </div>
        </div>

        <div id="chart-error" className="chart-error" style={{ display: 'none' }}></div>

        <div className="comparison-summary">
          <h3>Comparison Summary</h3>
          <div id="comparison-text" className="comparison-text">
            Select locations and metrics to view comparison
          </div>
        </div>

        <div className="daily-distribution">
          <h3>Daily Distribution</h3>
          <div className="chart-container">
            <canvas ref={distributionChartRef} id="distributionChart"></canvas>
          </div>
        </div>
      </main>
    </Layout>
  );
};

export default Comparison;
