import React from 'react';
import Layout from '../layouts/Layout';

const About: React.FC = () => {
  return (
    <Layout>
      <div className="container py-5">
        <div className="row justify-content-center">
          <div className="col-lg-10">
            <header className="text-center mb-5">
              <h1 className="display-4 fw-bold text-primary mb-3">About AirSense</h1>
              <p className="lead text-muted">
                Smart Indoor Air Quality Monitoring for a Healthier Environment
              </p>
            </header>

            <div className="row g-4 mb-5">
              <div className="col-md-6">
                <div className="card h-100 border-0 shadow-sm">
                  <div className="card-body p-4">
                    <div className="d-flex align-items-center mb-3">
                      <i className="bi bi-bullseye text-primary fs-2 me-3"></i>
                      <h3 className="card-title mb-0">Our Mission</h3>
                    </div>
                    <p className="card-text">
                      To provide real-time, accurate air quality monitoring solutions that help 
                      create healthier indoor environments for everyone. We believe that clean air 
                      is a fundamental right and strive to make air quality data accessible and actionable.
                    </p>
                  </div>
                </div>
              </div>

              <div className="col-md-6">
                <div className="card h-100 border-0 shadow-sm">
                  <div className="card-body p-4">
                    <div className="d-flex align-items-center mb-3">
                      <i className="bi bi-eye text-primary fs-2 me-3"></i>
                      <h3 className="card-title mb-0">Our Vision</h3>
                    </div>
                    <p className="card-text">
                      To be the leading platform for indoor air quality monitoring, empowering 
                      individuals and organizations to make informed decisions about their 
                      environment and health through cutting-edge technology and data insights.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="mb-5">
              <h2 className="text-center mb-4">What We Monitor</h2>
              <div className="row g-3">
                <div className="col-md-4">
                  <div className="text-center p-3">
                    <i className="bi bi-thermometer-half text-primary fs-1 mb-2"></i>
                    <h5>Temperature</h5>
                    <p className="text-muted small">Real-time temperature monitoring for optimal comfort</p>
                  </div>
                </div>
                <div className="col-md-4">
                  <div className="text-center p-3">
                    <i className="bi bi-droplet text-primary fs-1 mb-2"></i>
                    <h5>Humidity</h5>
                    <p className="text-muted small">Track humidity levels to prevent mold and discomfort</p>
                  </div>
                </div>
                <div className="col-md-4">
                  <div className="text-center p-3">
                    <i className="bi bi-cloud text-primary fs-1 mb-2"></i>
                    <h5>Air Quality</h5>
                    <p className="text-muted small">Monitor pollutants and particulate matter</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-light rounded p-4 mb-5">
              <h2 className="text-center mb-4">Why Choose AirSense?</h2>
              <div className="row g-4">
                <div className="col-md-6">
                  <div className="d-flex">
                    <i className="bi bi-check-circle-fill text-success fs-4 me-3 mt-1"></i>
                    <div>
                      <h5>Real-time Monitoring</h5>
                      <p className="mb-0 text-muted">Get instant updates on your indoor air quality conditions</p>
                    </div>
                  </div>
                </div>
                <div className="col-md-6">
                  <div className="d-flex">
                    <i className="bi bi-check-circle-fill text-success fs-4 me-3 mt-1"></i>
                    <div>
                      <h5>Easy Comparison</h5>
                      <p className="mb-0 text-muted">Compare air quality across different locations effortlessly</p>
                    </div>
                  </div>
                </div>
                <div className="col-md-6">
                  <div className="d-flex">
                    <i className="bi bi-check-circle-fill text-success fs-4 me-3 mt-1"></i>
                    <div>
                      <h5>Comprehensive Dashboard</h5>
                      <p className="mb-0 text-muted">Visualize trends and patterns with intuitive charts</p>
                    </div>
                  </div>
                </div>
                <div className="col-md-6">
                  <div className="d-flex">
                    <i className="bi bi-check-circle-fill text-success fs-4 me-3 mt-1"></i>
                    <div>
                      <h5>User-friendly Interface</h5>
                      <p className="mb-0 text-muted">Simple, clean design that anyone can use</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="text-center">
              <h2 className="mb-3">Ready to Improve Your Air Quality?</h2>
              <p className="text-muted mb-4">
                Start monitoring your indoor environment today and take the first step 
                towards a healthier lifestyle.
              </p>
              <a href="/dashboard" className="btn btn-primary btn-lg">
                Get Started
              </a>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default About;
