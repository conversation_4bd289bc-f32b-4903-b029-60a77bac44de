* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
  line-height: 1.6;
    background-image: url("/background.jpg");
    background-attachment: fixed;
    background-size: 100% 100%;
}

.search-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin: 0 auto;
}

.home-icon-btn {
  background: none;
  border: none;
  outline: none;
  cursor: pointer;
  padding: 0.4rem;
}

.home-icon {
  height: 32px;
  width: 32px;
  border: none;
  outline: none;
}

.location-dropdown {
  padding: 0.5rem 1rem;
  border-radius: 6px;
  border: 1px solid #ccc;
  font-size: 1rem;
  cursor: pointer;
}

.hero {
    text-align: center;
    padding: 50px 20px;
    background: url('/background.jpg') no-repeat center center/cover;
    color: #333;
}

.hero h1 {
    font-size: 50px;
    margin-bottom: 10px;
}

.hero h2 {
    font-size: 45px;
    font-weight: bold;
    color: #090909;
}

.hero p {
    font-size: 25px;
    margin-bottom: 20px;
}

.features {
    background-color: #dfdede;
    padding: 2rem 0;
    width: 100%;
}

.container1 {
    max-width: 100%;
    width: 100%;
    margin: 0;
    padding: 0 2rem;
}

.features h2 {
    font-family: 'Poppins', sans-serif;
    font-size: 40px;
    text-align: center;
    margin-bottom: 1.5rem;
}

.carousel {
    width: 100%;
    max-width: 100%;
}

.feature-item {
    background-color: #F7F7F7;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    margin: 0 auto;
    max-width: 800px;
}

.feature-item h3 {
    font-weight: bold;
    font-family: 'Poppins', sans-serif;
    font-size: 25px;
    color: #2A3E59;
    margin-bottom: 0.5rem;
}

.feature-item p {
    font-size: 20px;
    color: #555555;
}

.carousel-indicators button {
    background-color: #32B3A4;
}

.carousel-control-prev-icon,
.carousel-control-next-icon {
    background-color: #32B3A4;
    border-radius: 50%;
    width: 30px;
    height: 30px;
}

.footer {
    background-color: #2c3e50;
    color: white;
    padding: 1.5rem;
    text-align: center;
}

.footer-links {
    margin: 1rem 0;
}

.footer-links a {
    color: #bbbebd;
    text-decoration: none;
    margin: 0 1rem;
    font-weight: bold;
}

.footer-links a:hover {
    text-decoration: underline;
}

.footer-social-icons {
    margin-top: 1rem;
}

.footer-social-icons img {
    height: 50px;
    width: 50px;
    margin: 0 0.5rem;
    vertical-align: middle;
    border-radius: 10px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container1 {
        padding: 0 1rem;
    }
    
    .hero h1 {
        font-size: 35px;
    }
    
    .hero p {
        font-size: 18px;
    }
    
    .features h2 {
        font-size: 30px;
    }
    
    .feature-item h3 {
        font-size: 20px;
    }
    
    .feature-item p {
        font-size: 16px;
    }
}

@media (max-width: 480px) {
    .hero h1 {
        font-size: 28px;
    }
    
    .features h2 {
        font-size: 24px;
    }
    
    .feature-item {
        padding: 1.5rem;
    }
}
