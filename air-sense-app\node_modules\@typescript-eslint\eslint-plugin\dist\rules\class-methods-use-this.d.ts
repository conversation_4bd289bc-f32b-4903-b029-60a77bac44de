export type Options = [
    {
        enforceForClassFields?: boolean;
        exceptMethods?: string[];
        ignoreClassesThatImplementAnInterface?: boolean | 'public-fields';
        ignoreOverrideMethods?: boolean;
    }
];
export type MessageIds = 'missingThis';
declare const _default: import("@typescript-eslint/utils/ts-eslint").RuleModule<"missingThis", Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
export default _default;
//# sourceMappingURL=class-methods-use-this.d.ts.map