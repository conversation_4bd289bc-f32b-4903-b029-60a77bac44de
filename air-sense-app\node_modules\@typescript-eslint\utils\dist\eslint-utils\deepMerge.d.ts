export type ObjectLike<T = unknown> = Record<string, T>;
/**
 * Check if the variable contains an object strictly rejecting arrays
 * @returns `true` if obj is an object
 */
export declare function isObjectNotArray(obj: unknown): obj is ObjectLike;
/**
 * Pure function - doesn't mutate either parameter!
 * Merges two objects together deeply, overwriting the properties in first with the properties in second
 * @param first The first object
 * @param second The second object
 * @returns a new object
 */
export declare function deepMerge(first?: ObjectLike, second?: ObjectLike): Record<string, unknown>;
//# sourceMappingURL=deepMerge.d.ts.map