import React from 'react';

interface CircularGaugeProps {
  value: number;
  maxValue: number;
  minValue?: number;
  unit: string;
  title: string;
  status: 'good' | 'moderate' | 'poor';
  size?: number;
}

const CircularGauge: React.FC<CircularGaugeProps> = ({
  value,
  maxValue,
  minValue = 0,
  unit,
  title,
  status,
  size = 200
}) => {
  const radius = (size - 20) / 2;
  const circumference = 2 * Math.PI * radius;
  const percentage = ((value - minValue) / (maxValue - minValue)) * 100;
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (percentage / 100) * circumference;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'good': return '#28a745';
      case 'moderate': return '#ffc107';
      case 'poor': return '#dc3545';
      default: return '#6c757d';
    }
  };

  const getBackgroundColor = (status: string) => {
    switch (status) {
      case 'good': return 'rgba(40, 167, 69, 0.1)';
      case 'moderate': return 'rgba(255, 193, 7, 0.1)';
      case 'poor': return 'rgba(220, 53, 69, 0.1)';
      default: return 'rgba(108, 117, 125, 0.1)';
    }
  };

  return (
    <div className="circular-gauge" style={{ width: size, height: size, margin: '0 auto' }}>
      <div 
        className="gauge-container"
        style={{
          position: 'relative',
          width: size,
          height: size,
          background: getBackgroundColor(status),
          borderRadius: '50%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          flexDirection: 'column'
        }}
      >
        <svg
          width={size}
          height={size}
          style={{ position: 'absolute', top: 0, left: 0 }}
        >
          {/* Background circle */}
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            fill="none"
            stroke="#e9ecef"
            strokeWidth="8"
          />
          {/* Progress circle */}
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            fill="none"
            stroke={getStatusColor(status)}
            strokeWidth="8"
            strokeLinecap="round"
            strokeDasharray={strokeDasharray}
            strokeDashoffset={strokeDashoffset}
            transform={`rotate(-90 ${size / 2} ${size / 2})`}
            style={{
              transition: 'stroke-dashoffset 0.5s ease-in-out'
            }}
          />
        </svg>
        
        <div className="gauge-content" style={{ textAlign: 'center', zIndex: 1 }}>
          <div 
            className="gauge-value" 
            style={{ 
              fontSize: size > 150 ? '2rem' : '1.5rem', 
              fontWeight: 'bold',
              color: '#2c3e50',
              marginBottom: '0.25rem'
            }}
          >
            {typeof value === 'number' ? value.toFixed(1) : value}
            <span style={{ fontSize: '0.8em', fontWeight: 'normal' }}>{unit}</span>
          </div>
          <div 
            className="gauge-title" 
            style={{ 
              fontSize: size > 150 ? '0.9rem' : '0.7rem',
              color: '#6c757d',
              fontWeight: '500'
            }}
          >
            {title}
          </div>
        </div>
      </div>
      
      <div 
        className="gauge-status" 
        style={{ 
          textAlign: 'center', 
          marginTop: '0.5rem' 
        }}
      >
        <span 
          className={`badge`}
          style={{
            backgroundColor: getStatusColor(status),
            color: 'white',
            padding: '0.25rem 0.75rem',
            borderRadius: '1rem',
            fontSize: '0.75rem',
            fontWeight: '500',
            textTransform: 'capitalize'
          }}
        >
          {status}
        </span>
      </div>
    </div>
  );
};

export default CircularGauge;
