import React, { useEffect, useRef } from 'react';
import { Chart, ChartConfiguration, registerables } from 'chart.js';

Chart.register(...registerables);

interface CircularGaugeProps {
  value: number;
  maxValue: number;
  minValue?: number;
  unit: string;
  title: string;
  status: 'good' | 'moderate' | 'poor';
  size?: number;
  color?: string;
}

const CircularGauge: React.FC<CircularGaugeProps> = ({
  value,
  maxValue,
  minValue = 0,
  unit,
  title,
  status,
  size = 180,
  color
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const chartRef = useRef<Chart | null>(null);

  const percentage = Math.min(Math.max(((value - minValue) / (maxValue - minValue)) * 100, 0), 100);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'good': return '#34A853';
      case 'moderate': return '#FBBC05';
      case 'poor': return '#EA4335';
      default: return '#6c757d';
    }
  };

  const getBackgroundColor = (status: string) => {
    switch (status) {
      case 'good': return 'rgba(52, 168, 83, 0.1)';
      case 'moderate': return 'rgba(251, 188, 5, 0.1)';
      case 'poor': return 'rgba(234, 67, 53, 0.1)';
      default: return 'rgba(108, 117, 125, 0.1)';
    }
  };

  useEffect(() => {
    if (!canvasRef.current) return;

    // Destroy existing chart
    if (chartRef.current) {
      chartRef.current.destroy();
    }

    const ctx = canvasRef.current.getContext('2d');
    if (!ctx) return;

    const gaugeColor = color || getStatusColor(status);

    chartRef.current = new Chart(ctx, {
      type: 'doughnut',
      data: {
        datasets: [{
          data: [percentage, 100 - percentage],
          backgroundColor: [gaugeColor, '#f5f5f5'],
          borderWidth: 0
        }]
      },
      options: {
        circumference: 360,
        rotation: 270,
        cutout: '75%',
        plugins: {
          tooltip: { enabled: false },
          legend: { display: false }
        },
        animation: {
          animateRotate: true,
          animateScale: true
        },
        responsive: false,
        maintainAspectRatio: false
      }
    });

    return () => {
      if (chartRef.current) {
        chartRef.current.destroy();
      }
    };
  }, [value, percentage, status, color]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (chartRef.current) {
        chartRef.current.destroy();
      }
    };
  }, []);

  return (
    <div className="metric-card" style={{ background: getBackgroundColor(status) }}>
      <h3>{title}</h3>
      <div className="gauge-container">
        <canvas
          ref={canvasRef}
          width={size}
          height={size}
          style={{ width: `${size}px`, height: `${size}px` }}
        />
        <div className="value-display">
          <div className="value">
            {typeof value === 'number' ? value.toFixed(1) : value}
          </div>
          <div className="unit">{unit}</div>
        </div>
      </div>
      <div className={`status ${status}`}>{status}</div>
    </div>
  );
};

export default CircularGauge;
