import React from 'react';

interface CircularGaugeProps {
  value: number;
  maxValue: number;
  minValue?: number;
  unit: string;
  title: string;
  status: 'good' | 'moderate' | 'poor';
  size?: number;
  color?: string;
}

const CircularGauge: React.FC<CircularGaugeProps> = ({
  value,
  maxValue,
  minValue = 0,
  unit,
  title,
  status,
  size = 200,
  color
}) => {
  const radius = (size - 20) / 2;
  const circumference = 2 * Math.PI * radius;
  const percentage = ((value - minValue) / (maxValue - minValue)) * 100;
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (percentage / 100) * circumference;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'good': return '#28a745';
      case 'moderate': return '#ffc107';
      case 'poor': return '#dc3545';
      default: return '#6c757d';
    }
  };

  const getBackgroundColor = (status: string) => {
    switch (status) {
      case 'good': return 'rgba(40, 167, 69, 0.1)';
      case 'moderate': return 'rgba(255, 193, 7, 0.1)';
      case 'poor': return 'rgba(220, 53, 69, 0.1)';
      default: return 'rgba(108, 117, 125, 0.1)';
    }
  };

  return (
    <div
      className="circular-gauge"
      style={{
        width: '100%',
        height: '100%',
        margin: '0 auto',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'space-between',
        minHeight: '300px',
        maxWidth: '250px'
      }}
    >
      <div
        className="gauge-container"
        style={{
          position: 'relative',
          width: size,
          height: size,
          minWidth: size,
          minHeight: size,
          background: getBackgroundColor(status),
          borderRadius: '50%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          flexDirection: 'column',
          flexShrink: 0,
          aspectRatio: '1',
          overflow: 'hidden'
        }}
      >
        <svg
          width={size}
          height={size}
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            overflow: 'visible'
          }}
          viewBox={`0 0 ${size} ${size}`}
          preserveAspectRatio="xMidYMid meet"
        >
          {/* Background circle */}
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            fill="none"
            stroke="#e9ecef"
            strokeWidth="8"
          />
          {/* Progress circle */}
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            fill="none"
            stroke={color || getStatusColor(status)}
            strokeWidth="8"
            strokeLinecap="round"
            strokeDasharray={strokeDasharray}
            strokeDashoffset={strokeDashoffset}
            transform={`rotate(-90 ${size / 2} ${size / 2})`}
            style={{
              transition: 'stroke-dashoffset 0.5s ease-in-out'
            }}
          />
        </svg>
        
        <div className="gauge-content" style={{ textAlign: 'center', zIndex: 1 }}>
          <div 
            className="gauge-value" 
            style={{ 
              fontSize: size > 150 ? '2rem' : '1.5rem', 
              fontWeight: 'bold',
              color: '#2c3e50',
              marginBottom: '0.25rem'
            }}
          >
            {typeof value === 'number' ? value.toFixed(1) : value}
            <span style={{ fontSize: '0.8em', fontWeight: 'normal' }}>{unit}</span>
          </div>
          <div 
            className="gauge-title" 
            style={{ 
              fontSize: size > 150 ? '0.9rem' : '0.7rem',
              color: '#6c757d',
              fontWeight: '500'
            }}
          >
            {title}
          </div>
        </div>
      </div>
      
      <div
        className="gauge-status"
        style={{
          textAlign: 'center',
          marginTop: '1rem',
          flexShrink: 0,
          width: '100%',
          maxWidth: size
        }}
      >
        <span
          className={`badge`}
          style={{
            backgroundColor: getStatusColor(status),
            color: 'white',
            padding: '0.4rem 1rem',
            borderRadius: '1rem',
            fontSize: '0.8rem',
            fontWeight: '500',
            textTransform: 'capitalize',
            display: 'inline-block',
            minWidth: '60px'
          }}
        >
          {status}
        </span>
      </div>
    </div>
  );
};

export default CircularGauge;
