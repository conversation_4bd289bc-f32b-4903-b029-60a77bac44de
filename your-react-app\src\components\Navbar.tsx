import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';

const Navbar: React.FC = () => {
  const [selectedLocation, setSelectedLocation] = useState<string>('');
  const location = useLocation();

  useEffect(() => {
    // Restore selected location from localStorage
    const storedLocation = localStorage.getItem('userLocation');
    if (storedLocation) {
      setSelectedLocation(storedLocation);
    }
  }, []);

  const handleLocationChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const location = event.target.value;
    setSelectedLocation(location);
    localStorage.setItem('userLocation', location);
  };

  const handleHomeClick = () => {
    localStorage.removeItem('userLocation');
    setSelectedLocation('');
  };

  const isActive = (path: string) => location.pathname === path;

  return (
    <nav className="navbar navbar-expand-lg navbar-light bg-light">
      <div className="container-fluid">
        <Link className="navbar-brand" to="/">
          <img src="/logo.jpg" alt="AirSense Logo" style={{ height: '60px', borderRadius: '10px' }} />
        </Link>
        
        <div className="navbar-nav mx-auto">
          <Link 
            className={`nav-link ${isActive('/') ? 'active fw-bold' : ''}`} 
            to="/"
          >
            Home
          </Link>
          <Link 
            className={`nav-link ${isActive('/dashboard') ? 'active fw-bold' : ''}`} 
            to="/dashboard"
          >
            Dashboard
          </Link>
          <Link 
            className={`nav-link ${isActive('/comparison') ? 'active fw-bold' : ''}`} 
            to="/comparison"
          >
            Comparison
          </Link>
        </div>

        <div className="d-flex align-items-center gap-3">
          <button 
            onClick={handleHomeClick}
            className="btn btn-outline-secondary p-2"
            title="Replace the location"
          >
            <img src="/home.png" alt="Home" style={{ height: '32px', width: '32px' }} />
          </button>

          <select 
            value={selectedLocation}
            onChange={handleLocationChange}
            className="form-select"
            style={{ width: 'auto' }}
            title="Only change the location when changing the air quality condition measurement location"
          >
            <option value="" disabled>Select your location</option>
            <option value="deans-office">Dean's Office</option>
            <option value="basement">Basement</option>
            <option value="lecture-hall">Lecture Hall</option>
          </select>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
