import type { TSESTree } from '@typescript-eslint/types';
import { DefinitionBase } from './DefinitionBase';
import { DefinitionType } from './DefinitionType';
declare class ImplicitGlobalVariableDefinition extends DefinitionBase<DefinitionType.ImplicitGlobalVariable, TSESTree.Node, null, TSESTree.BindingName> {
    constructor(name: TSESTree.BindingName, node: ImplicitGlobalVariableDefinition['node']);
    readonly isTypeDefinition = false;
    readonly isVariableDefinition = true;
}
export { ImplicitGlobalVariableDefinition };
//# sourceMappingURL=ImplicitGlobalVariableDefinition.d.ts.map