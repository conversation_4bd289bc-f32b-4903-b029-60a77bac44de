{"version": 3, "file": "getOperatorPrecedence.js", "sourceRoot": "", "sources": ["../../src/util/getOperatorPrecedence.ts"], "names": [], "mappings": ";;;AAEA,oDAA0D;AAC1D,2CAAwC;AAExC,IAAY,kBA8LX;AA9LD,WAAY,kBAAkB;IAC5B,cAAc;IACd,2BAA2B;IAC3B,0CAA0C;IAC1C,6DAAK,CAAA;IAEL,iFAAiF;IACjF,iBAAiB;IACjB,iCAAiC;IACjC,+DAAM,CAAA;IAEN,wBAAwB;IACxB,4BAA4B;IAC5B,sBAAsB;IACtB,oBAAoB;IACpB,yBAAyB;IACzB,sDAAsD;IACtD,qEAAqE;IACrE,EAAE;IACF,6FAA6F;IAC7F,mCAAmC;IAEnC,wCAAwC;IACxC,mBAAmB;IACnB,cAAc;IACd,mCAAmC;IACnC,uCAAuC;IACvC,6DAAK,CAAA;IAEL,wEAAwE;IACxE,uFAAuF;IACvF,6BAA6B;IAC7B,uEAAuE;IACvE,uEAAU,CAAA;IAEV,4FAA4F;IAC5F,6BAA6B;IAC7B,8CAA8C;IAC9C,yBAAyB;IACzB,6BAA6B;IAC7B,+EAA+E;IAC/E,0BAA0B;IAC1B,0BAA0B;IAC1B,yBAAyB;IACzB,yEAAW,CAAA;IAEX,sBAAsB;IACtB,sDAAsD;IACtD,0BAA0B;IAC1B,yBAAyB;IACzB,0BAA0B;IAC1B,mEAAsB,CAAA;IAEtB,uBAAuB;IACvB,2BAA2B;IAC3B,oDAAoD;IACpD,qEAAS,CAAA;IAET,wBAAwB;IACxB,0BAA0B;IAC1B,oDAAoD;IACpD,uEAAU,CAAA;IAEV,uBAAuB;IACvB,2BAA2B;IAC3B,mDAAmD;IACnD,qEAAS,CAAA;IAET,wBAAwB;IACxB,2BAA2B;IAC3B,oDAAoD;IACpD,uEAAU,CAAA;IAEV,wBAAwB;IACxB,yBAAyB;IACzB,kDAAkD;IAClD,uEAAU,CAAA;IAEV,sBAAsB;IACtB,2BAA2B;IAC3B,mDAAmD;IACnD,mDAAmD;IACnD,oDAAoD;IACpD,oDAAoD;IACpD,oEAAQ,CAAA;IAER,wBAAwB;IACxB,sBAAsB;IACtB,+CAA+C;IAC/C,+CAA+C;IAC/C,gDAAgD;IAChD,gDAAgD;IAChD,wDAAwD;IACxD,gDAAgD;IAChD,mDAAmD;IACnD,wEAAU,CAAA;IAEV,mBAAmB;IACnB,yBAAyB;IACzB,8CAA8C;IAC9C,8CAA8C;IAC9C,+CAA+C;IAC/C,8DAAK,CAAA;IAEL,sBAAsB;IACtB,+BAA+B;IAC/B,sDAAsD;IACtD,sDAAsD;IACtD,oEAAQ,CAAA;IAER,4BAA4B;IAC5B,+BAA+B;IAC/B,+EAA+E;IAC/E,+CAA+C;IAC/C,gFAAc,CAAA;IAEd,4BAA4B;IAC5B,sBAAsB;IACtB,qDAAqD;IACrD,gFAAc,CAAA;IAEd,mBAAmB;IACnB,uBAAuB;IACvB,+BAA+B;IAC/B,6BAA6B;IAC7B,+BAA+B;IAC/B,0BAA0B;IAC1B,0BAA0B;IAC1B,0BAA0B;IAC1B,0BAA0B;IAC1B,sBAAsB;IACtB,uFAAuF;IACvF,2BAA2B;IAC3B,2BAA2B;IAC3B,8DAAK,CAAA;IAEL,oBAAoB;IACpB,6BAA6B;IAC7B,kCAAkC;IAClC,kCAAkC;IAClC,gEAAM,CAAA;IAEN,0BAA0B;IAC1B,oBAAoB;IACpB,qBAAqB;IACrB,iBAAiB;IACjB,uBAAuB;IACvB,0BAA0B;IAC1B,4EAAY,CAAA;IAEZ,kBAAkB;IAClB,2CAA2C;IAC3C,gBAAgB;IAChB,iBAAiB;IACjB,+BAA+B;IAC/B,wCAAwC;IACxC,wCAAwC;IACxC,qCAAqC;IACrC,oBAAoB;IACpB,wBAAwB;IACxB,0CAA0C;IAC1C,0CAA0C;IAC1C,uCAAuC;IACvC,oBAAoB;IACpB,mBAAmB;IACnB,uCAAuC;IACvC,gEAAM,CAAA;IAEN,oBAAoB;IACpB,qBAAqB;IACrB,aAAa;IACb,0BAA0B;IAC1B,cAAc;IACd,mBAAmB;IACnB,oBAAoB;IACpB,yBAAyB;IACzB,sBAAsB;IACtB,0BAA0B;IAC1B,8BAA8B;IAC9B,+BAA+B;IAC/B,+BAA+B;IAC/B,sBAAsB;IACtB,wDAAwD;IACxD,kEAAO,CAAA;IAEP,kEAAiB,CAAA;IACjB,+DAAc,CAAA;IACd,oFAAoF;IACpF,mBAAmB;IACnB,kEAAY,CAAA;AACd,CAAC,EA9LW,kBAAkB,kCAAlB,kBAAkB,QA8L7B;AAED,SAAgB,4BAA4B,CAC1C,IAAmB;IAEnB,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;QAClB,KAAK,sBAAc,CAAC,aAAa,CAAC;QAClC,KAAK,sBAAc,CAAC,WAAW;YAC7B,OAAO,kBAAkB,CAAC,MAAM,CAAC;QAEnC,KAAK,sBAAc,CAAC,eAAe;YACjC,OAAO,kBAAkB,CAAC,KAAK,CAAC;QAElC,KAAK,sBAAc,CAAC,qBAAqB;YACvC,OAAO,kBAAkB,CAAC,WAAW,CAAC;QAExC,KAAK,sBAAc,CAAC,kBAAkB;YACpC,OAAO,kBAAkB,CAAC,KAAK,CAAC;QAElC,KAAK,sBAAc,CAAC,oBAAoB,CAAC;QACzC,KAAK,sBAAc,CAAC,gBAAgB,CAAC;QACrC,KAAK,sBAAc,CAAC,iBAAiB;YACnC,QAAQ,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACtB,KAAK,IAAI,CAAC;gBACV,KAAK,IAAI,CAAC;gBACV,KAAK,IAAI,CAAC;gBACV,KAAK,KAAK,CAAC;gBACX,KAAK,IAAI,CAAC;gBACV,KAAK,IAAI,CAAC;gBACV,KAAK,IAAI,CAAC;gBACV,KAAK,KAAK,CAAC;gBACX,KAAK,KAAK,CAAC;gBACX,KAAK,MAAM,CAAC;gBACZ,KAAK,IAAI,CAAC;gBACV,KAAK,IAAI,CAAC;gBACV,KAAK,IAAI,CAAC;gBACV,KAAK,KAAK,CAAC;gBACX,KAAK,KAAK,CAAC;gBACX,KAAK,KAAK;oBACR,OAAO,kBAAkB,CAAC,UAAU,CAAC;gBAEvC;oBACE,OAAO,2BAA2B,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACtD,CAAC;QAEH,KAAK,sBAAc,CAAC,eAAe,CAAC;QACpC,KAAK,sBAAc,CAAC,mBAAmB,CAAC;QACxC,KAAK,sBAAc,CAAC,eAAe,CAAC;QACpC,KAAK,sBAAc,CAAC,eAAe;YACjC,OAAO,kBAAkB,CAAC,KAAK,CAAC;QAElC,KAAK,sBAAc,CAAC,gBAAgB;YAClC,yEAAyE;YACzE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;gBAChB,OAAO,kBAAkB,CAAC,KAAK,CAAC;YAClC,CAAC;YACD,OAAO,kBAAkB,CAAC,MAAM,CAAC;QAEnC,KAAK,sBAAc,CAAC,eAAe;YACjC,OAAO,4BAA4B,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAEvD,KAAK,sBAAc,CAAC,cAAc;YAChC,OAAO,kBAAkB,CAAC,YAAY,CAAC;QAEzC,KAAK,sBAAc,CAAC,aAAa;YAC/B,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC;gBAC9B,CAAC,CAAC,kBAAkB,CAAC,MAAM;gBAC3B,CAAC,CAAC,kBAAkB,CAAC,YAAY,CAAC;QAEtC,KAAK,sBAAc,CAAC,wBAAwB,CAAC;QAC7C,KAAK,sBAAc,CAAC,gBAAgB,CAAC;QACrC,KAAK,sBAAc,CAAC,YAAY;YAC9B,OAAO,kBAAkB,CAAC,MAAM,CAAC;QAEnC,KAAK,sBAAc,CAAC,cAAc;YAChC,OAAO,kBAAkB,CAAC,UAAU,CAAC;QAEvC,KAAK,sBAAc,CAAC,cAAc,CAAC;QACnC,KAAK,sBAAc,CAAC,KAAK,CAAC;QAC1B,KAAK,sBAAc,CAAC,UAAU,CAAC;QAC/B,KAAK,sBAAc,CAAC,iBAAiB,CAAC;QACtC,KAAK,sBAAc,CAAC,OAAO,CAAC;QAC5B,KAAK,sBAAc,CAAC,eAAe,CAAC;QACpC,KAAK,sBAAc,CAAC,gBAAgB,CAAC;QACrC,KAAK,sBAAc,CAAC,kBAAkB,CAAC;QACvC,KAAK,sBAAc,CAAC,uBAAuB,CAAC;QAC5C,KAAK,sBAAc,CAAC,eAAe,CAAC;QACpC,KAAK,sBAAc,CAAC,eAAe,CAAC;QACpC,KAAK,sBAAc,CAAC,UAAU,CAAC;QAC/B,KAAK,sBAAc,CAAC,WAAW;YAC7B,sCAAsC;YACtC,2CAA2C;YAC3C,qCAAqC;YACrC,OAAO,kBAAkB,CAAC,OAAO,CAAC;QAEpC;YACE,OAAO,kBAAkB,CAAC,OAAO,CAAC;IACtC,CAAC;AACH,CAAC;AAhGD,oEAgGC;AAMD,SAAgB,qBAAqB,CACnC,QAAoB,EACpB,YAAwB,EACxB,YAAsB;IAEtB,QAAQ,QAAQ,EAAE,CAAC;QACjB,uFAAuF;QACvF,KAAK,uBAAU,CAAC,mBAAmB;YACjC,OAAO,kBAAkB,CAAC,KAAK,CAAC;QAElC,KAAK,uBAAU,CAAC,aAAa;YAC3B,OAAO,kBAAkB,CAAC,MAAM,CAAC;QAEnC,KAAK,uBAAU,CAAC,eAAe;YAC7B,OAAO,kBAAkB,CAAC,KAAK,CAAC;QAElC,KAAK,uBAAU,CAAC,qBAAqB;YACnC,OAAO,kBAAkB,CAAC,WAAW,CAAC;QAExC,KAAK,uBAAU,CAAC,gBAAgB;YAC9B,QAAQ,YAAY,EAAE,CAAC;gBACrB,KAAK,uBAAU,CAAC,UAAU;oBACxB,OAAO,kBAAkB,CAAC,KAAK,CAAC;gBAElC,KAAK,uBAAU,CAAC,WAAW,CAAC;gBAC5B,KAAK,uBAAU,CAAC,eAAe,CAAC;gBAChC,KAAK,uBAAU,CAAC,gBAAgB,CAAC;gBACjC,KAAK,uBAAU,CAAC,2BAA2B,CAAC;gBAC5C,KAAK,uBAAU,CAAC,mBAAmB,CAAC;gBACpC,KAAK,uBAAU,CAAC,gBAAgB,CAAC;gBACjC,KAAK,uBAAU,CAAC,kBAAkB,CAAC;gBACnC,KAAK,uBAAU,CAAC,2BAA2B,CAAC;gBAC5C,KAAK,uBAAU,CAAC,iCAAiC,CAAC;gBAClD,KAAK,uBAAU,CAAC,4CAA4C,CAAC;gBAC7D,KAAK,uBAAU,CAAC,oBAAoB,CAAC;gBACrC,KAAK,uBAAU,CAAC,gBAAgB,CAAC;gBACjC,KAAK,uBAAU,CAAC,cAAc,CAAC;gBAC/B,KAAK,uBAAU,CAAC,iBAAiB,CAAC;gBAClC,KAAK,uBAAU,CAAC,6BAA6B,CAAC;gBAC9C,KAAK,uBAAU,CAAC,2BAA2B;oBACzC,OAAO,kBAAkB,CAAC,UAAU,CAAC;gBAEvC;oBACE,OAAO,2BAA2B,CAAC,YAAY,CAAC,CAAC;YACrD,CAAC;QAEH,yEAAyE;QACzE,KAAK,uBAAU,CAAC,uBAAuB,CAAC;QACxC,KAAK,uBAAU,CAAC,iBAAiB,CAAC;QAClC,KAAK,uBAAU,CAAC,qBAAqB,CAAC;QACtC,KAAK,uBAAU,CAAC,gBAAgB,CAAC;QACjC,KAAK,uBAAU,CAAC,cAAc,CAAC;QAC/B,KAAK,uBAAU,CAAC,gBAAgB,CAAC;QACjC,KAAK,uBAAU,CAAC,eAAe;YAC7B,OAAO,kBAAkB,CAAC,KAAK,CAAC;QAElC,KAAK,uBAAU,CAAC,sBAAsB;YACpC,OAAO,kBAAkB,CAAC,MAAM,CAAC;QAEnC,KAAK,uBAAU,CAAC,cAAc;YAC5B,OAAO,kBAAkB,CAAC,YAAY,CAAC;QAEzC,KAAK,uBAAU,CAAC,aAAa;YAC3B,OAAO,YAAY;gBACjB,CAAC,CAAC,kBAAkB,CAAC,MAAM;gBAC3B,CAAC,CAAC,kBAAkB,CAAC,YAAY,CAAC;QAEtC,KAAK,uBAAU,CAAC,wBAAwB,CAAC;QACzC,KAAK,uBAAU,CAAC,wBAAwB,CAAC;QACzC,KAAK,uBAAU,CAAC,uBAAuB,CAAC;QACxC,KAAK,uBAAU,CAAC,YAAY;YAC1B,OAAO,kBAAkB,CAAC,MAAM,CAAC;QAEnC,KAAK,uBAAU,CAAC,YAAY,CAAC;QAC7B,KAAK,uBAAU,CAAC,mBAAmB;YACjC,OAAO,kBAAkB,CAAC,UAAU,CAAC;QAEvC,KAAK,uBAAU,CAAC,WAAW,CAAC;QAC5B,KAAK,uBAAU,CAAC,YAAY,CAAC;QAC7B,KAAK,uBAAU,CAAC,UAAU,CAAC;QAC3B,KAAK,uBAAU,CAAC,iBAAiB,CAAC;QAClC,KAAK,uBAAU,CAAC,WAAW,CAAC;QAC5B,KAAK,uBAAU,CAAC,WAAW,CAAC;QAC5B,KAAK,uBAAU,CAAC,YAAY,CAAC;QAC7B,KAAK,uBAAU,CAAC,cAAc,CAAC;QAC/B,KAAK,uBAAU,CAAC,aAAa,CAAC;QAC9B,KAAK,uBAAU,CAAC,aAAa,CAAC;QAC9B,KAAK,uBAAU,CAAC,sBAAsB,CAAC;QACvC,KAAK,uBAAU,CAAC,uBAAuB,CAAC;QACxC,KAAK,uBAAU,CAAC,kBAAkB,CAAC;QACnC,KAAK,uBAAU,CAAC,aAAa,CAAC;QAC9B,KAAK,uBAAU,CAAC,eAAe,CAAC;QAChC,KAAK,uBAAU,CAAC,wBAAwB,CAAC;QACzC,KAAK,uBAAU,CAAC,6BAA6B,CAAC;QAC9C,KAAK,uBAAU,CAAC,kBAAkB,CAAC;QACnC,KAAK,uBAAU,CAAC,uBAAuB,CAAC;QACxC,KAAK,uBAAU,CAAC,iBAAiB,CAAC;QAClC,KAAK,uBAAU,CAAC,UAAU,CAAC;QAC3B,KAAK,uBAAU,CAAC,qBAAqB,CAAC;QACtC,KAAK,uBAAU,CAAC,WAAW;YACzB,OAAO,kBAAkB,CAAC,OAAO,CAAC;QAEpC;YACE,OAAO,kBAAkB,CAAC,OAAO,CAAC;IACtC,CAAC;AACH,CAAC;AAzGD,sDAyGC;AAED,SAAgB,2BAA2B,CACzC,IAAuC;IAEvC,QAAQ,IAAI,EAAE,CAAC;QACb,KAAK,uBAAU,CAAC,qBAAqB,CAAC;QACtC,KAAK,IAAI;YACP,OAAO,kBAAkB,CAAC,QAAQ,CAAC;QAErC,KAAK,uBAAU,CAAC,WAAW,CAAC;QAC5B,KAAK,IAAI;YACP,OAAO,kBAAkB,CAAC,SAAS,CAAC;QAEtC,KAAK,uBAAU,CAAC,uBAAuB,CAAC;QACxC,KAAK,IAAI;YACP,OAAO,kBAAkB,CAAC,UAAU,CAAC;QAEvC,KAAK,uBAAU,CAAC,QAAQ,CAAC;QACzB,KAAK,GAAG;YACN,OAAO,kBAAkB,CAAC,SAAS,CAAC;QAEtC,KAAK,uBAAU,CAAC,UAAU,CAAC;QAC3B,KAAK,GAAG;YACN,OAAO,kBAAkB,CAAC,UAAU,CAAC;QAEvC,KAAK,uBAAU,CAAC,cAAc,CAAC;QAC/B,KAAK,GAAG;YACN,OAAO,kBAAkB,CAAC,UAAU,CAAC;QAEvC,KAAK,uBAAU,CAAC,iBAAiB,CAAC;QAClC,KAAK,IAAI,CAAC;QACV,KAAK,uBAAU,CAAC,sBAAsB,CAAC;QACvC,KAAK,IAAI,CAAC;QACV,KAAK,uBAAU,CAAC,uBAAuB,CAAC;QACxC,KAAK,KAAK,CAAC;QACX,KAAK,uBAAU,CAAC,4BAA4B,CAAC;QAC7C,KAAK,KAAK;YACR,OAAO,kBAAkB,CAAC,QAAQ,CAAC;QAErC,KAAK,uBAAU,CAAC,aAAa,CAAC;QAC9B,KAAK,GAAG,CAAC;QACT,KAAK,uBAAU,CAAC,gBAAgB,CAAC;QACjC,KAAK,GAAG,CAAC;QACT,KAAK,uBAAU,CAAC,mBAAmB,CAAC;QACpC,KAAK,IAAI,CAAC;QACV,KAAK,uBAAU,CAAC,sBAAsB,CAAC;QACvC,KAAK,IAAI,CAAC;QACV,KAAK,uBAAU,CAAC,iBAAiB,CAAC;QAClC,KAAK,YAAY,CAAC;QAClB,KAAK,uBAAU,CAAC,SAAS,CAAC;QAC1B,KAAK,IAAI,CAAC;QACV,KAAK,uBAAU,CAAC,SAAS;YACvB,+CAA+C;YAC/C,OAAO,kBAAkB,CAAC,UAAU,CAAC;QAEvC,KAAK,uBAAU,CAAC,qBAAqB,CAAC;QACtC,KAAK,IAAI,CAAC;QACV,KAAK,uBAAU,CAAC,2BAA2B,CAAC;QAC5C,KAAK,IAAI,CAAC;QACV,KAAK,uBAAU,CAAC,sCAAsC,CAAC;QACvD,KAAK,KAAK;YACR,OAAO,kBAAkB,CAAC,KAAK,CAAC;QAElC,KAAK,uBAAU,CAAC,SAAS,CAAC;QAC1B,KAAK,GAAG,CAAC;QACT,KAAK,uBAAU,CAAC,UAAU,CAAC;QAC3B,KAAK,GAAG;YACN,OAAO,kBAAkB,CAAC,QAAQ,CAAC;QAErC,KAAK,uBAAU,CAAC,aAAa,CAAC;QAC9B,KAAK,GAAG,CAAC;QACT,KAAK,uBAAU,CAAC,UAAU,CAAC;QAC3B,KAAK,GAAG,CAAC;QACT,KAAK,uBAAU,CAAC,YAAY,CAAC;QAC7B,KAAK,GAAG;YACN,OAAO,kBAAkB,CAAC,cAAc,CAAC;QAE3C,KAAK,uBAAU,CAAC,qBAAqB,CAAC;QACtC,KAAK,IAAI;YACP,OAAO,kBAAkB,CAAC,cAAc,CAAC;IAC7C,CAAC;IAED,qFAAqF;IACrF,mBAAmB;IACnB,OAAO,CAAC,CAAC,CAAC;AACZ,CAAC;AApFD,kEAoFC"}