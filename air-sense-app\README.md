# AirSense - Smart Indoor Air Quality Monitoring System

A modern React application built with Next.js and Tailwind CSS for monitoring indoor air quality.

## Features

- **Real-Time Monitoring**: Live air quality readings tailored to your environment
- **Historical Data Access**: Analyze past data to identify patterns and ensure long-term safety
- **Air Quality Measurement**: Continuous monitoring of CO2, VOCs, particulate matter, and more
- **Location Comparison**: Compare air quality metrics between different locations
- **Responsive Design**: Works seamlessly on desktop and mobile devices

## Tech Stack

- **Framework**: Next.js 15 with App Router
- **Styling**: Tailwind CSS
- **Language**: TypeScript
- **Components**: React with hooks
- **Images**: Next.js Image optimization

## Project Structure

```
air-sense-app/
├── src/
│   ├── app/                    # Next.js App Router pages
│   │   ├── dashboard/          # Dashboard page
│   │   ├── comparison/         # Comparison page
│   │   ├── layout.tsx          # Root layout
│   │   ├── page.tsx            # Home page
│   │   └── globals.css         # Global styles
│   ├── components/             # Reusable React components
│   │   ├── Header.tsx          # Navigation header
│   │   ├── Footer.tsx          # Footer component
│   │   ├── Layout.tsx          # Page layout wrapper
│   │   └── Carousel.tsx        # Features carousel
│   └── images/                 # Source images (for reference)
├── public/
│   └── images/                 # Public images served by Next.js
└── package.json
```

## Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn

### Installation

1. Navigate to the project directory:
   ```bash
   cd air-sense-app
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Run the development server:
   ```bash
   npm run dev
   ```

4. Open [http://localhost:3000](http://localhost:3000) in your browser

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

## Pages

### Home Page (`/`)
- Welcome section with hero image
- Features carousel showcasing system capabilities
- Location selector in header

### Dashboard (`/dashboard`)
- Air quality metrics display
- Historical data visualization
- Real-time monitoring interface

### Comparison (`/comparison`)
- Side-by-side location comparison
- Metric selection and date filtering
- Visual comparison charts

## Components

### Header
- Navigation menu
- Location selector with localStorage persistence
- Responsive design

### Footer
- Copyright information
- Social media links
- Additional navigation links

### Carousel
- Auto-advancing feature showcase
- Manual navigation controls
- Responsive image display

## Styling

The application uses Tailwind CSS for styling with:
- Responsive design patterns
- Custom color schemes for air quality status
- Smooth transitions and hover effects
- Modern card-based layouts

## Data

Currently uses mock data for demonstration. In a production environment, this would connect to:
- Real-time sensor APIs
- Historical data databases
- Location-based services

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

## License

This project is licensed under the MIT License.
