<!DOCTYPE html>
<html>
<head>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha3/dist/css/bootstrap.min.css" rel="stylesheet">
    <title>AirSense - Smart Indoor Air Quality Monitoring</title>
    <link rel="stylesheet" href="home.css">
    
</head>
<body>
  
    <header>
      <nav class="nav-bar">
        <div class="logo-container">
            <div class="logo-wrapper">
                <img src="logo.jpg" alt="AirGuard Logo" class="logo">
                
            </div>
        </div>
        
        <div class="nav-links">
            <a href="home2.html" class="active">Home</a>
            <a href="dashboard.html" >Dashboard</a>
            <a href="comparison.html">Comparison</a>
        </div>
        <div class="search-container">
            <button class="home-icon-btn" id="homeIconBtn" title="Replace the location">
              <img src="home.png" alt="Home" class="home-icon">
            </button>

            <select id="locationDropdown" class="location-dropdown" title="Only change the location when changing the air quality condition measurement location">
              <option value="" disabled selected>Select your location</option>
              <option value="deans-office">Dean's Office</option>
              <option value="basement">Basement</option>
              <option value="lecture-hall">Lecture Hall</option>
            </select>
        </div>
      </nav>

    </header>
 
        
    <section class="hero">

        <h1><b>Welcome to the Smart Indoor Air Quality Monitoring System</b></h1><br>
        <img src="logo.jpg" style="height: 400px;border-radius: 30px;">
        <br>
        <p>Track, analyze, and improve indoor air quality with ease</p>
       
        
        
        <section class="features">
          <div class="container1">
            <h2 style="font-size: 40px;">Our Features</h2>
            <div id="featureCarousel" class="carousel slide" data-bs-ride="carousel">
              <!-- Indicators -->
              <div class="carousel-indicators">
                <button type="button" data-bs-target="#featureCarousel" data-bs-slide-to="0" class="active" aria-current="true" aria-label="Feature 1"></button>
                <button type="button" data-bs-target="#featureCarousel" data-bs-slide-to="1" aria-label="Feature 2"></button>
                <button type="button" data-bs-target="#featureCarousel" data-bs-slide-to="2" aria-label="Feature 3"></button>
                <button type="button" data-bs-target="#featureCarousel" data-bs-slide-to="3" aria-label="Feature 4"></button>
                <button type="button" data-bs-target="#featureCarousel" data-bs-slide-to="4" aria-label="Feature 5"></button>
              </div>
        
              <!-- Slides -->
              <div class="carousel-inner">
                <!-- Feature 1 -->
                <div class="carousel-item active">
                  <div class="feature-item text-center">
                    <h3>Real-Time Monitoring</h3>
                    <img src="live_air.jpg" alt="Location Selection" style="height: 300px;">
                    <p>Stay updated with live air quality readings tailored to your environment..</p>
                  </div>
                </div>
                <!-- Feature 2 -->
                
                <!-- Feature 3 -->
                <div class="carousel-item">
                  <div class="feature-item text-center">
                    <h3>Access to Historical Data</h3>
                    <img src="history.jpg" alt="Historical Data" style="height: 300px;">
                    <p>Analyze past data to identify patterns and ensure long-term safety.</p>
                  </div>
                </div>
                <!-- Feature 4 -->
                <div class="carousel-item">
                  <div class="feature-item text-center">
                    <h3>Air Quality Measurement</h3>
                    <img src="air_index.jpg" alt="Air Quality Measurement" style="height: 300px;">
                    <p>Continuously measures air quality parameters, including CO2, VOCs, particulate matter, and more.</p>
                  </div>
                </div>
                <!-- Feature 5 -->
                <div class="carousel-item">
                  <div class="feature-item text-center">
                    <h3>Comparison of Air Quality</h3>
                    <img src="comparison.png" alt="Air Quality Comparison" style="height: 300px;">
                    <p>Displays graphical comparisons of air quality metrics between two selected locations effectively.</p>
                  </div>
                </div>
              </div>
        
              <!-- Controls -->
              <button class="carousel-control-prev" type="button" data-bs-target="#featureCarousel" data-bs-slide="prev">
                <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                <span class="visually-hidden">Previous</span>
              </button>
              <button class="carousel-control-next" type="button" data-bs-target="#featureCarousel" data-bs-slide="next">
                <span class="carousel-control-next-icon" aria-hidden="true"></span>
                <span class="visually-hidden">Next</span>
              </button>
            </div>
          </div>
        </section><br><br>
  
  </form>
  
    </section>
    <div class="footer">
        <p>&copy; 2025 Meridian Innovators. All Rights Reserved.</p>
        <div class="footer-links">
          <a href="#">Privacy Policy</a>
          <a href="#">Terms of Service</a>
          <a href="#">Contact Us</a>
        </div>
        <div class="footer-social-icons">
          <a href="#"><img src="fb.avif" alt="Facebook" ></a>
          <a href="#"><img src="twitter.webp" alt="Twitter"></a>
          <a href="#"><img src="linkedin.webp " alt="LinkedIn" ></a>
        </div>
      </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="test.js"></script>
</body>
</html>
