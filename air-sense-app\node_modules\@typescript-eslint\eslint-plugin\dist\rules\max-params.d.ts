import type { TSESTree } from '@typescript-eslint/utils';
import type { InferMessageIdsTypeFromRule, InferOptionsTypeFromRule } from '../util';
declare const baseRule: import("@typescript-eslint/utils/ts-eslint").RuleModule<"exceed", ({
    countVoidThis?: boolean;
    max: number;
} | {
    countVoidThis?: boolean;
    maximum: number;
})[], unknown, {
    ArrowFunctionExpression(node: TSESTree.ArrowFunctionExpression): void;
    FunctionDeclaration(node: TSESTree.FunctionDeclaration | TSESTree.TSDeclareFunction | TSESTree.TSFunctionType): void;
    FunctionExpression(node: TSESTree.FunctionExpression): void;
}>;
export type Options = InferOptionsTypeFromRule<typeof baseRule>;
export type MessageIds = InferMessageIdsTypeFromRule<typeof baseRule>;
declare const _default: import("@typescript-eslint/utils/ts-eslint").RuleModule<"exceed", ({
    countVoidThis?: boolean;
    max: number;
} | {
    countVoidThis?: boolean;
    maximum: number;
})[], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
export default _default;
//# sourceMappingURL=max-params.d.ts.map