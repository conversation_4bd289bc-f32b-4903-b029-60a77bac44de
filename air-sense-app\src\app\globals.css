@import "tailwindcss";

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
  line-height: 1.6;
  background-color: #f8fafc;
}

/* Custom styles for air quality components */
.hero-section {
  background-image: url('/images/background.jpg');
  background-attachment: fixed;
  background-size: cover;
  background-position: center;
}

.air-quality-good {
  color: #10b981;
}

.air-quality-moderate {
  color: #f59e0b;
}

.air-quality-poor {
  color: #ef4444;
}
