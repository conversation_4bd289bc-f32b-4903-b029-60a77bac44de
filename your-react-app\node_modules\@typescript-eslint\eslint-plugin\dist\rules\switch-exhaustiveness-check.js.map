{"version": 3, "file": "switch-exhaustiveness-check.js", "sourceRoot": "", "sources": ["../../src/rules/switch-exhaustiveness-check.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,wEAAsE;AACtE,sDAAwC;AACxC,+CAAiC;AAEjC,kCAOiB;AAiCjB,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,6BAA6B;IACnC,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EAAE,iDAAiD;YAC9D,oBAAoB,EAAE,IAAI;SAC3B;QACD,cAAc,EAAE,IAAI;QACpB,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,mCAAmC,EAAE;wBACnC,WAAW,EAAE,8EAA8E;wBAC3F,IAAI,EAAE,SAAS;qBAChB;oBACD,yBAAyB,EAAE;wBACzB,WAAW,EAAE,wEAAwE;wBACrF,IAAI,EAAE,SAAS;qBAChB;iBACF;gBACD,oBAAoB,EAAE,KAAK;aAC5B;SACF;QACD,QAAQ,EAAE;YACR,qBAAqB,EACnB,kEAAkE;YACpE,oBAAoB,EAClB,yEAAyE;YAC3E,eAAe,EAAE,iCAAiC;SACnD;KACF;IACD,cAAc,EAAE;QACd;YACE,mCAAmC,EAAE,IAAI;YACzC,yBAAyB,EAAE,KAAK;SACjC;KACF;IACD,MAAM,CACJ,OAAO,EACP,CAAC,EAAE,mCAAmC,EAAE,yBAAyB,EAAE,CAAC;QAEpE,MAAM,UAAU,GAAG,IAAA,4BAAa,EAAC,OAAO,CAAC,CAAC;QAC1C,MAAM,QAAQ,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;QAC5C,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAClD,MAAM,eAAe,GAAG,QAAQ,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC;QAE9D,SAAS,iBAAiB,CAAC,IAA8B;YACvD,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CACjC,UAAU,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,IAAI,IAAI,CACtC,CAAC;YAEF,MAAM,gBAAgB,GAAG,IAAA,mCAA4B,EACnD,QAAQ,EACR,IAAI,CAAC,YAAY,CAClB,CAAC;YAEF,MAAM,UAAU,GAAG,gBAAgB,CAAC,SAAS,EAAE,EAAE,WAEpC,CAAC;YAEd,MAAM,sBAAsB,GAC1B,6BAA6B,CAAC,gBAAgB,CAAC,CAAC;YAElD,MAAM,SAAS,GAAG,IAAI,GAAG,EAAW,CAAC;YACrC,KAAK,MAAM,UAAU,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;gBACpC,wEAAwE;gBACxE,kBAAkB;gBAClB,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC;oBAC5B,SAAS;gBACX,CAAC;gBAED,MAAM,QAAQ,GAAG,IAAA,mCAA4B,EAC3C,QAAQ,EACR,UAAU,CAAC,IAAI,CAChB,CAAC;gBACF,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAC1B,CAAC;YAED,MAAM,yBAAyB,GAAc,EAAE,CAAC;YAEhD,KAAK,MAAM,SAAS,IAAI,OAAO,CAAC,cAAc,CAAC,gBAAgB,CAAC,EAAE,CAAC;gBACjE,KAAK,MAAM,gBAAgB,IAAI,OAAO,CAAC,qBAAqB,CAC1D,SAAS,CACV,EAAE,CAAC;oBACF,IACE,SAAS,CAAC,GAAG,CAAC,gBAAgB,CAAC;wBAC/B,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,EACxC,CAAC;wBACD,SAAS;oBACX,CAAC;oBAED,yBAAyB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBACnD,CAAC;YACH,CAAC;YAED,OAAO;gBACL,UAAU;gBACV,yBAAyB;gBACzB,WAAW;gBACX,sBAAsB;aACvB,CAAC;QACJ,CAAC;QAED,SAAS,qBAAqB,CAC5B,IAA8B,EAC9B,cAA8B;YAE9B,MAAM,EAAE,yBAAyB,EAAE,UAAU,EAAE,WAAW,EAAE,GAC1D,cAAc,CAAC;YAEjB,0EAA0E;YAC1E,uEAAuE;YACvE,gCAAgC;YAChC,IAAI,yBAAyB,CAAC,MAAM,GAAG,CAAC,IAAI,WAAW,KAAK,SAAS,EAAE,CAAC;gBACtE,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,IAAI,CAAC,YAAY;oBACvB,SAAS,EAAE,uBAAuB;oBAClC,IAAI,EAAE;wBACJ,eAAe,EAAE,yBAAyB;6BACvC,GAAG,CAAC,WAAW,CAAC,EAAE,CACjB,OAAO,CAAC,aAAa,CAAC,WAAW,EAAE,EAAE,CAAC,SAAS,CAAC,YAAY,CAAC;4BAC3D,CAAC,CAAC,UAAU,WAAW,CAAC,SAAS,EAAE,EAAE,WAAqB,EAAE;4BAC5D,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,WAAW,CAAC,CACtC;6BACA,IAAI,CAAC,KAAK,CAAC;qBACf;oBACD,OAAO,EAAE;wBACP;4BACE,SAAS,EAAE,iBAAiB;4BAC5B,GAAG,CAAC,KAAK;gCACP,OAAO,SAAS,CACd,KAAK,EACL,IAAI,EACJ,yBAAyB,EACzB,UAAU,EAAE,QAAQ,EAAE,CACvB,CAAC;4BACJ,CAAC;yBACF;qBACF;iBACF,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,SAAS,SAAS,CAChB,KAAyB,EACzB,IAA8B,EAC9B,kBAAsC,EAAE,4BAA4B;QACpE,UAAmB;YAEnB,MAAM,QAAQ,GACZ,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YACnE,MAAM,UAAU,GAAG,QAAQ;gBACzB,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC;gBACvC,CAAC,CAAC,qEAAqE;oBACrE,+CAA+C;oBAC/C,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YAEtC,MAAM,YAAY,GAAG,EAAE,CAAC;YACxB,KAAK,MAAM,iBAAiB,IAAI,kBAAkB,EAAE,CAAC;gBACnD,IAAI,iBAAiB,IAAI,IAAI,EAAE,CAAC;oBAC9B,YAAY,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;oBAClE,SAAS;gBACX,CAAC;gBAED,MAAM,iBAAiB,GAAG,iBAAiB,CAAC,SAAS,EAAE,EAAE,WAAW,CAAC;gBACrE,IAAI,QAAQ,GAAG,OAAO,CAAC,aAAa,CAClC,iBAAiB,EACjB,EAAE,CAAC,SAAS,CAAC,YAAY,CAC1B;oBACC,CAAC,CAAC,iBAAkB;oBACpB,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,iBAAiB,CAAC,CAAC;gBAE5C,IACE,UAAU;oBACV,CAAC,iBAAiB,IAAI,iBAAiB,KAAK,EAAE,CAAC;oBAC/C,IAAA,sBAAe,EAAC,iBAAiB,CAAC,QAAQ,EAAE,EAAE,eAAe,CAAC,MAAM,CAAC,EACrE,CAAC;oBACD,MAAM,iBAAiB,GAAG,iBAAiB;yBACxC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC;yBACpB,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC;yBACrB,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;oBAEzB,QAAQ,GAAG,GAAG,UAAU,KAAK,iBAAiB,IAAI,CAAC;gBACrD,CAAC;gBAED,MAAM,YAAY,GAAG,wBAAwB,QAAQ,OAAO,CAAC;gBAC7D,MAAM,mBAAmB,GAAG,YAAY,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;gBAE9D,YAAY,CAAC,IAAI,CACf,QAAQ,QAAQ,wBAAwB,mBAAmB,MAAM,CAClE,CAAC;YACJ,CAAC;YAED,MAAM,SAAS,GAAG,YAAY;iBAC3B,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,UAAU,GAAG,IAAI,EAAE,CAAC;iBACnC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEd,IAAI,QAAQ,EAAE,CAAC;gBACb,OAAO,KAAK,CAAC,eAAe,CAAC,QAAQ,EAAE,KAAK,SAAS,EAAE,CAAC,CAAC;YAC3D,CAAC;YAED,gCAAgC;YAChC,MAAM,YAAY,GAAG,UAAU,CAAC,aAAa,CAC3C,IAAI,CAAC,YAAY,EACjB,0BAAmB,CACnB,CAAC;YACH,MAAM,YAAY,GAAG,UAAU,CAAC,aAAa,CAC3C,IAAI,CAAC,YAAY,EACjB,0BAAmB,CACnB,CAAC;YAEH,OAAO,KAAK,CAAC,gBAAgB,CAC3B,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAC9C,CAAC,GAAG,EAAE,SAAS,EAAE,GAAG,UAAU,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAC9C,CAAC;QACJ,CAAC;QAED,SAAS,iCAAiC,CACxC,cAA8B;YAE9B,IAAI,mCAAmC,EAAE,CAAC;gBACxC,OAAO;YACT,CAAC;YAED,MAAM,EAAE,yBAAyB,EAAE,WAAW,EAAE,sBAAsB,EAAE,GACtE,cAAc,CAAC;YAEjB,IACE,yBAAyB,CAAC,MAAM,KAAK,CAAC;gBACtC,WAAW,KAAK,SAAS;gBACzB,CAAC,sBAAsB,EACvB,CAAC;gBACD,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,WAAW;oBACjB,SAAS,EAAE,sBAAsB;iBAClC,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,SAAS,6BAA6B,CACpC,IAA8B,EAC9B,cAA8B;YAE9B,IAAI,CAAC,yBAAyB,EAAE,CAAC;gBAC/B,OAAO;YACT,CAAC;YAED,MAAM,EAAE,WAAW,EAAE,sBAAsB,EAAE,GAAG,cAAc,CAAC;YAE/D,IAAI,sBAAsB,IAAI,WAAW,KAAK,SAAS,EAAE,CAAC;gBACxD,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,IAAI,CAAC,YAAY;oBACvB,SAAS,EAAE,uBAAuB;oBAClC,IAAI,EAAE;wBACJ,eAAe,EAAE,SAAS;qBAC3B;oBACD,OAAO,EAAE;wBACP;4BACE,SAAS,EAAE,iBAAiB;4BAC5B,GAAG,CAAC,KAAK;gCACP,OAAO,SAAS,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;4BACxC,CAAC;yBACF;qBACF;iBACF,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO;YACL,eAAe,CAAC,IAAI;gBAClB,MAAM,cAAc,GAAG,iBAAiB,CAAC,IAAI,CAAC,CAAC;gBAE/C,qBAAqB,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;gBAC5C,iCAAiC,CAAC,cAAc,CAAC,CAAC;gBAClD,6BAA6B,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;YACtD,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC;AAEH,SAAS,qBAAqB,CAAC,IAAa;IAC1C,OAAO,OAAO,CAAC,aAAa,CAC1B,IAAI,EACJ,EAAE,CAAC,SAAS,CAAC,OAAO;QAClB,EAAE,CAAC,SAAS,CAAC,SAAS;QACtB,EAAE,CAAC,SAAS,CAAC,IAAI;QACjB,EAAE,CAAC,SAAS,CAAC,cAAc,CAC9B,CAAC;AACJ,CAAC;AAED;;;;;;;;GAQG;AACH,SAAS,6BAA6B,CAAC,IAAa;IAClD,OAAO,OAAO;SACX,cAAc,CAAC,IAAI,CAAC;SACpB,IAAI,CAAC,IAAI,CAAC,EAAE,CACX,OAAO;SACJ,qBAAqB,CAAC,IAAI,CAAC;SAC3B,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC,CACrD,CAAC;AACN,CAAC"}